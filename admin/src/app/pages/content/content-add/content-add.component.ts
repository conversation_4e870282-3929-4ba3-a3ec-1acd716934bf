import {ChangeDetectorRef, Component, ElementRef, inject, ViewChild, ViewEncapsulation} from '@angular/core';
import {CKEditorModule} from "@ckeditor/ckeditor5-angular";
import { NgLabelTemplateDirective, NgOptionTemplateDirective, NgSelectComponent } from '@ng-select/ng-select';
import {FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators} from "@angular/forms";
import {CommonModule} from "@angular/common";
import {ContentService} from "@/services/content.service";
import {ActivatedRoute, Router} from "@angular/router";
import {LanguagesEnum} from "@/enums/languages.enum";
import { isPlatformBrowser, Location } from '@angular/common';
import { Inject, PLATFORM_ID } from '@angular/core';
import {
  AccessibilityHelp,
  Autoformat,
  AutoLink,
  Autosave,
  BalloonToolbar,
  BlockQuote,
  Bold,
  ClassicEditor,
  Code,
  type EditorConfig,
  Element,
  Essentials,
  FindAndReplace,
  FullPage,
  GeneralHtmlSupport,
  Heading,
  HorizontalLine,
  HtmlComment,
  HtmlEmbed,
  Indent,
  IndentBlock,
  Italic,
  Link,
  Mention,
  Paragraph,
  SelectAll,
  ShowBlocks,
  SourceEditing,
  SpecialCharacters,
  SpecialCharactersArrows,
  SpecialCharactersCurrency,
  SpecialCharactersEssentials,
  SpecialCharactersLatin,
  SpecialCharactersMathematical,
  SpecialCharactersText,
  Strikethrough,
  TextPartLanguage,
  TextTransformation,
  Underline,
  Undo,
  Image,
  ImageCaption,
  ImageResize,
  ImageStyle,
  ImageToolbar,
  LinkImage,
  ImageUpload,
  FileRepository,
  SimpleUploadAdapter,
  List,
  Table,
  TableToolbar,
  RemoveFormat
} from 'ckeditor5';
import {environment} from "../../../../environments/environment";
import {AudioService} from "@/services/audio.service";
import {FileService} from "@/services/file.service";
import {PhotopreviewComponent} from "@/components/photopreview/photopreview.component";
import { CustomUploadAdapter } from './custom-upload-adapter';

@Component({
    selector: 'app-content-add',
    imports: [CKEditorModule, ReactiveFormsModule, CommonModule, NgSelectComponent, PhotopreviewComponent],
    templateUrl: './content-add.component.html',
    styleUrl: './content-add.component.scss',
    encapsulation: ViewEncapsulation.None
})
export class ContentAddComponent {
  @ViewChild('dialog') dialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('addButtonDialog') addButtonDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('addAudioDialog') addAudioDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('linkedContentDialog') linkedContentDialog!: ElementRef<HTMLDialogElement>;
  message: string = '';
  editorInstance: any
  selectedLanguage = 'ru'
  Editor = ClassicEditor
  editorConfig: EditorConfig = {};
  fb = inject(FormBuilder)
  router = inject(Router)
  route = inject(ActivatedRoute)
  contentService = inject(ContentService)
  changeDetector = inject(ChangeDetectorRef)
  audioService = inject(AudioService)
  fileService = inject(FileService)
  languages = Object.keys(LanguagesEnum)
  addContentForms: { lang: string, form: FormGroup }[] = []
  slug = this.route.snapshot.params['slug']
  category = null
  lanIdx = 0;
  isLayoutReady = false
  tags: any
  audiofiles_all: any = []
  currentForm: any = null
  buttonForm = this.fb.group({
    index: null,
    name: null,
    link: null,
  })
  audioForm = this.fb.group({
    index: null,
    audio: null
  })
  authors: any = []
  linkedNum: number = 0
  allLinks: any = []
  replaceLinkForm = this.fb.group({
    replace: ''
  })

  get tagsArray() {
    return (form: FormGroup) => (form.get('tags') as FormArray).value
  }

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private location: Location
  ) {}

  get buttons() {
    return this.currentForm.get('buttons') as FormArray
  }

  get audiofiles() {
    return this.currentForm.get('audiofiles') as FormArray
  }

  goBack(): void {
    if (isPlatformBrowser(this.platformId)) {
      if (window.history.length > 1) {
        this.location.back();
      } else {
        this.router.navigate(['/']);
      }
    } else {
      console.log('Back button not available in SSR.');
    }
  }

  public ngAfterViewInit(): void {
    const componentContext = this;
    this.editorConfig = {
      mention: {
        feeds: [
          {
            marker: '@',
            feed: this.getFeedItems.bind(this),
            minimumCharacters: 0
          }
        ]
      },
      toolbar: {
        items: [
          'undo',
          'redo',
          '|',
          'sourceEditing',
          'showBlocks',
          '|',
          'heading',
          '|',
          'bold',
          'italic',
          'underline',
          'removeFormat',
          '|',
          'blockQuote',
          '|',
          'outdent',
          'indent',
          '|',
          'specialCharacters',
          'specialCharactersEssentials',
          '|',
          'uploadImage',
          'imageStyle:inline',
          'imageStyle:block',
          'imageStyle:side',
          'toggleImageCaption', 'imageTextAlternative', 'ckboxImageEdit',
          'link',
          // 'linkImage',
          '|',
          'insertLink',
          'editLink',
          'unlink',
          '|',
          'bulletedList',
          'numberedList',
          'insertTable',
          'mediaEmbed'
          // 'LinkImage'
        ],
        shouldNotGroupWhenFull: false
      },

      plugins: [
        AccessibilityHelp,
        Autoformat,
        AutoLink,
        Autosave,
        BalloonToolbar,
        BlockQuote,
        RemoveFormat,
        Bold,
        Code,
        Essentials,
        FindAndReplace,
        FullPage,
        GeneralHtmlSupport,
        Heading,
        HorizontalLine,
        HtmlComment,
        HtmlEmbed,
        Indent,
        IndentBlock,
        Italic,
        Link,
        Paragraph,
        SelectAll,
        ShowBlocks,
        SourceEditing,
        SpecialCharacters,
        SpecialCharactersArrows,
        SpecialCharactersCurrency,
        SpecialCharactersEssentials,
        SpecialCharactersLatin,
        SpecialCharactersMathematical,
        SpecialCharactersText,
        Strikethrough,
        TextPartLanguage,
        TextTransformation,
        Underline,
        Undo,
        Mention,
        Image,
        ImageToolbar,
        ImageCaption,
        ImageStyle,
        ImageResize,
        LinkImage,
        Image,
        ImageToolbar,
        ImageCaption,
        ImageStyle,
        ImageResize,
        ImageUpload,
        FileRepository,
        SimpleUploadAdapter,
        List,
        Table,
        TableToolbar,
        // MediaEmbed,
        function (editor) {

          editor.model.schema.setAttributeProperties('fontColor', {
            isFormatting: true
          });

          editor.model.schema.setAttributeProperties('fontBackgroundColor', {
            isFormatting: true
          });

          editor.model.schema.setAttributeProperties('htmlAttributes', {
            isFormatting: true
          });

          editor.model.schema.setAttributeProperties('htmlSpan', {
            isFormatting: true
          });

          editor.model.schema.setAttributeProperties('style', {
            isFormatting: true
          });

          editor.model.schema.setAttributeProperties('linkHref', {
            isFormatting: true,
            copyOnEnter: false,
            isAllowed: () => false
          });

          editor.conversion.for('upcast').elementToAttribute({
            view: {
              name: 'a',
              key: 'data-mention',
              classes: 'mention',
              attributes: {
                href: true,
                'data-content-id': true,
                'data-id': true
              }
            },
            model: {
              key: 'mention',
              value: (viewItem: Element) => {


                return editor.plugins.get('Mention').toMentionAttribute(viewItem, {
                  link: viewItem.getAttribute('href'),
                  content_id: viewItem.getAttribute('data-content-id')
                });
              }
            },
            converterPriority: 'high'
          } );

          editor.conversion.for('downcast').attributeToElement({
            model: 'mention',
            view: (modelAttributeValue, {writer}) => {

              const selection = componentContext.editorInstance.model.document.selection;
              const position = selection.getFirstPosition();

              setTimeout(() => {
                const currentForm = componentContext.addContentForms.find(e => e.lang == componentContext.selectedLanguage);
                if (currentForm) {
                    currentForm.form.patchValue({
                        content: componentContext.editorInstance.getData()
                    });

                    // Restore caret position after content update
                    componentContext.editorInstance.model.change((writer: any) => {
                        writer.setSelection(position);
                    });
                }
              }, 1000)

              if(!modelAttributeValue) return false

              const linkElement = writer.createAttributeElement('a', {
                class: 'mention',
                'data-mention': modelAttributeValue.id,
                'data-content-id': modelAttributeValue.content_id,
                'href': modelAttributeValue.link,
                'routerLink': modelAttributeValue.link, // Add Angular router directive
                'data-angular-link': 'true' // Add a marker to identify this as an Angular router link
              }, {
                priority: 20,
                id: modelAttributeValue.uid
              });

              return linkElement;
            }
          })
        }
      ],

      balloonToolbar: ['bold', 'italic', '|', 'link'],
      image: {

        resizeOptions: [
          {
              name: 'resizeImage:original',
              value: null,
              icon: 'original'
          },
          {
              name: 'resizeImage:custom',
              value: 'custom',
              icon: 'custom'
          },
          {
              name: 'resizeImage:50',
              value: '50',
              icon: 'medium'
          },
          {
              name: 'resizeImage:75',
              value: '75',
              icon: 'large'
          }
      ],
        toolbar: [
          'toggleImageCaption',
          'imageTextAlternative',
          'ckboxImageEdit',
          'resizeImage:50',
          'resizeImage:75',
          'resizeImage:original',
          'resizeImage:custom',
          'imageStyle:alignLeft',
          'imageStyle:alignCenter',
          'imageStyle:alignRight',
          '|',
          'imageResize'

        ]
      },
      heading: {
        options: [
          {
            model: 'paragraph',
            title: 'Paragraph',
            class: 'ck-heading_paragraph'
          },
          {
            model: 'heading1',
            view: 'h1',
            title: 'Heading 1',
            class: 'ck-heading_heading1'
          },
          // {
          //   model: 'heading2',
          //   view: 'h2',
          //   title: 'Heading 2',
          //   class: 'ck-heading_heading2'
          // },
          // {
          //   model: 'heading3',
          //   view: 'h3',
          //   title: 'Heading 3',
          //   class: 'ck-heading_heading3'
          // },
          // {
          //   model: 'heading4',
          //   view: 'h4',
          //   title: 'Heading 4',
          //   class: 'ck-heading_heading4'
          // },
          // {
          //   model: 'heading5',
          //   view: 'h5',
          //   title: 'Heading 5',
          //   class: 'ck-heading_heading5'
          // },
          // {
          //   model: 'heading6',
          //   view: 'h6',
          //   title: 'Heading 6',
          //   class: 'ck-heading_heading6'
          // }
        ]
      },
      htmlSupport: {
        allow: [
          {
            name: /^.*$/,  // Все элементы
            attributes: true,
            classes: true,
            styles: {
              // Разрешаем только определенные стили, исключая цвета
              'text-align': true,
              'margin': true,
              'padding': true,
              'font-weight': true,
              'font-style': true,
              'text-decoration': true,
              // Исключаем цвета, не указывая их здесь:
              // 'color': true,
              // 'background-color': true,
              // 'background': true,
            }
          }
        ],
        // Можно также явно запретить определенные стили
        disallow: [
          {
            name: /^.*$/,
            styles: {
              'color': true,
              'background-color': true,
              'background': true
            }
          }
        ]
      },
      link: {
        addTargetToExternalLinks: true,
        defaultProtocol: 'https://',
        decorators: {
          toggleDownloadable: {
            mode: 'manual',
            label: 'Downloadable',
            attributes: {
              download: 'file'
            }
          }
        },
      },
      list: {
        properties: {
          styles: true,
          startIndex: true,
          reversed: true
        }
      },
      menuBar: {
        isVisible: true
      },

    };

    this.isLayoutReady = true;
    this.changeDetector.detectChanges();
  }

  ngOnInit() {
    this.audioService.getTags().subscribe(res => this.tags = res)
    this.audioService.getAuthors().subscribe((res: any) => this.authors = res)
    this.contentService.getCategories()
    this.audioService.getAudioFiles().subscribe((res: any) => this.audiofiles_all = res.items)

    this.fillForm()
    if(this.slug) this.getContent()
    this.currentForm = this.addContentForms.find((e: any) => e.lang == this.selectedLanguage)!.form
  }

  getContent() {
    this.contentService.getBySlug(this.slug).subscribe((res: any) => {
      for(let item of this.addContentForms) {
        const itemLang = res.find((k: any) => k.lang == item.lang)
        if(!itemLang) continue
        if(!this.category) this.category = itemLang?.category?.id

        item.form.patchValue({
          ...itemLang, category: itemLang?.category?.id,
          tags: itemLang.tags.map((e: any) => e.id)
        })

        const buttonsArray: FormArray = this.fb.array([]);
        const buttons = itemLang.buttons || []
        delete itemLang.buttons;

        if(buttons) {
          for (let button of buttons) {
            buttonsArray.push(this.fb.group(button));
          }
          item.form.setControl('buttons', buttonsArray);
        }

        const audioFilesArray: FormArray = this.fb.array([]);
        const audioFiles = itemLang.audioFiles;
        delete itemLang.audioFiles;

        if(audioFiles) {
          for (let item of audioFiles) {
            audioFilesArray.push(this.fb.group({index: null, audio: item}));
          }
          item.form.setControl('audiofiles', audioFilesArray);
        }
      }
    })
  }

  fillForm() {
    this.languages.forEach(lang => {
      this.addContentForms.push({
        lang,
        form: this.fb.group({
          active: [true],
          slug: [this.slug || ''],
          title: [''],
          seo_title: [''],
          seo_description: [''],
          content: [''],
          category: [this.category || 2],
          telegram: [null],
          email: [null],
          instagram: [null],
          tags: [null, Validators.required],
          phone: [null],
          preview: [null, Validators.required],
          buttons: this.fb.array([]),
          audiofiles: this.fb.array([]),
          author: [null],
          youtube: [null],
        })
      })
    })
  }

  onEditorReady(editor: any) {
    this.editorInstance = editor;
    this.editorInstance.plugins.get("FileRepository").createUploadAdapter = (loader: any) => {
      return new CustomUploadAdapter(loader, `${environment.apiUrl}/file/ck/upload`);
    };

    // this.editorInstance.model.document.on('change:data', () => {
    //   const removedItems = Array.from(editor.model.document.differ.getChanges()).filter((change: any) => change.type === "remove")
    //   removedItems.forEach((removedItem: any) => {
    //     const src = removedItem.attributes.get("src")
    //     if (src) {
    //       this.fileService.deleteByPath(src, 'content').subscribe()
    //       console.log("Deleted image URL:", src)
    //     }
    //   })
    // })
  }


  selectLanguage(lang: string) {
    this.selectedLanguage = lang
    switch (lang) {
      case 'RU':
        this.lanIdx = 0;
        break;
      case 'EN':
        this.lanIdx = 1;
        break;
      case 'DE':
        this.lanIdx = 2;
        break;
      default:
        break;
    }
    const editorInstance = this.editorInstance
    if (editorInstance) {
      setTimeout(() => {
        const content = this.addContentForms.find(e => e.lang == this.selectedLanguage)!.form.get('content')?.value
        editorInstance.setData(content); // Устанавливаем новое значение в CKEditor
      }, 100)
    }
    this.currentForm = this.addContentForms.find((e: any) => e.lang == this.selectedLanguage)!.form;
  }

  addValidationFormCategory() {
    let isValid = true;
    const values = this.addContentForms[this.lanIdx].form.value;
    if (!values.category) {
      isValid = false;
    }
    return isValid;
  }

  addValidationFormSEO() {
    let isValid = true;
    const values = this.addContentForms[this.lanIdx].form.value;
    if (!values.seo_description || values.seo_description.length < 2) {
      isValid = false;
    }
    return isValid;
  }

  addValidationFormSubmit() {
    let isValid = true;
    const values = this.addContentForms[this.lanIdx].form.value;
    if (!values.title || values.title.length < 2) {
      isValid = false;
    }
    if (!values.seo_title || values.seo_title.length < 2) {
      isValid = false;
    }
    if (!values.tags || values.tags.length < 1) {
      isValid = false;
    }
    if (!values.seo_description || values.seo_description.length < 2) {
      isValid = false;
    }
    if (!values.category) {
      isValid = false;
    }
    if(!values.preview) {
      isValid = false;
    }
    return isValid;
  }

  addContentFormSubmit() {
    const category = this.addContentForms.find(e => e.lang == 'ru')!.form.value.category;
    const form = this.addContentForms.map(e => ({ ...e, form: e.lang === 'ru' ? e.form.value : {...e.form.value, category, buttons: (e.form.get('buttons') as FormArray).value} }));
    if (this.slug) {
      return this.contentService.update(this.slug, form).subscribe({
        next: () => {
          this.openDialog('Контент успешно обновлен.');
        },
        error: (err) => {
          this.openDialog('Контент не обновлен. Попробуйте еще.');
        }
      });
    }

    return this.contentService.addContent(form).subscribe({
      next: (res: any) => {
        this.openDialog('Контент успешно добавлен.');
        this.router.navigateByUrl('/content/' + res.slug);
      },
      error: (err) => {
        this.openDialog('Контент не добавлен. Попробуйте еще.');
      }
    });
  }

  openDialog(message: string) {
    this.message = message;
    this.dialog.nativeElement.showModal()
  }

  close(dialog: any) {
    dialog.close();
    if (this.message == 'Контент успешно добавлен.') {
      this.router.navigate(['/content']);
    }
    if (this.message == 'Контент успешно удален.') {
      this.router.navigate(['/content']);
    }
    if (this.message == 'Контент успешно обновлен.') {
      if(this.route.snapshot.params['slug'] !== this.addContentForms[0].form.get('slug')?.value){
        this.router.navigate(['/content/' + this.addContentForms[0].form.get('slug')?.value]);
      }

    }
  }

  back() {
    this.router.navigate(['/content']);
  }

  deleteWithCheckLinks() {
    this.contentService.getLinkedContent(this.slug).subscribe((num: any) => {
      if(num) {
        this.linkedNum = num;
        this.contentService.getAll().subscribe((res: any) => {
          this.allLinks = res.filter((item: any) => item.slug !== this.slug);
        })

        this.linkedContentDialog.nativeElement.showModal()
      } else {
        this.replaceLinkForm.reset()
        this.delete()
      }
    })
  }

  delete() {
    this.contentService.delete(this.slug, this.replaceLinkForm.value.replace).subscribe({
      next: () => {
        this.openDialog('Контент успешно удален.');
      },
      error: (err) => {
        this.openDialog('Контент не удален. Попробуйте еще.');
      }
    });
  }

  onCategoryChange(e: any) {
    this.addContentForms.map(k => k.form.patchValue({category: +e.target.value}))
  }

  async getFeedItems(query: string): Promise<any[]> {
    return this.contentService.searchPages(query).toPromise().then((results: any) => {
      const filteredResults = results.filter((item: any) => item.slug !== this.slug);

      return filteredResults.map((item: any) => ({
        id: `@${item.title}`,
        content_id: item.id,
        name: item.title,
        link: `/${item.lang}/categories/${item.category.id}/${item.slug}`,
        text: item.title
      }));
    });
  }

  copyLink(item: any) {
    window.navigator.clipboard.writeText(`${environment.clientUrl}${item.lang}/categories/${item.form.value.category}/${this.slug}`)
  }

  redirect(item: any) {
    window.open(`${environment.clientUrl}${item.lang}/categories/${item.form.value.category}/${this.slug}`, '_blank');
  }

  uploadPreview(e: Event, form: FormGroup) {
    const files = (e.target as HTMLInputElement).files!

    this.fileService.uploadToTempFolder(files).subscribe((res: any) => {
      this.currentForm.get('preview')?.setValue(res[0])
      // this.addContentForms.map(k => k.form.patchValue({
      //   preview: res[0]
      // }))
    })
  }

  showAddButtonForm() {
    this.addButtonDialog.nativeElement.showModal()
  }

  showAddAudioForm() {
    this.addAudioDialog.nativeElement.showModal()
  }

  closeAddAudioForm() {
    this.addAudioDialog.nativeElement.close()
    this.audioForm.reset()
  }

  closeAddButtonForm() {
    this.addButtonDialog.nativeElement.close()
    this.buttonForm.reset()
  }

  addButton() {
    const buttons = this.currentForm.get('buttons') as FormArray;
    if(this.buttonForm.value.index !== null) {
      buttons.at(this.buttonForm.value.index!).patchValue(this.buttonForm.value)
    } else {
      buttons.push(this.fb.group(this.buttonForm.value))
    }
    this.closeAddButtonForm()
  }

  addAudio() {
    const audiofiles = this.currentForm.get('audiofiles') as FormArray;
    if(this.audioForm.value.index !== null) {
      audiofiles.at(this.audioForm.value.index!).patchValue(this.audioForm.value)
    } else {
      audiofiles.push(this.fb.group(this.audioForm.value))
    }
    this.closeAddAudioForm()
  }

  editButton(index: number, button: any) {
    button.index = index
    this.buttonForm.patchValue(button)
    this.showAddButtonForm()
  }

  editAudio(index: number, audio: any) {
    audio.index = index
    this.audioForm.patchValue(audio)
    this.showAddAudioForm()
  }

  removeButton(index: number) {
    this.currentForm.get('buttons').removeAt(index)
  }

  removeAudio(index: number) {
    this.currentForm.get('audiofiles').removeAt(index)
  }

  closeModal() {
    this.linkedContentDialog.nativeElement.close()
  }

  // Add this property to the class
isEditorFullscreen = false;

// Add this method to the class
toggleEditorFullscreen(): void {
  this.isEditorFullscreen = !this.isEditorFullscreen;

  // Prevent body scrolling when in fullscreen mode
  if (this.isEditorFullscreen) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = '';
  }

  // Refresh the editor to adjust to new container size
  if (this.editorInstance) {
    this.editorInstance.editing.view.change((writer: any) => {
      // This forces a refresh of the editing view
    });
  }

  // Focus the editor after toggling fullscreen
  setTimeout(() => {
    if (this.editorInstance) {
      this.editorInstance.editing.view.focus();
    }
  }, 100);
}
  clearAuthor() {
    // Найти текущую форму для выбранного языка
    const currentForm = this.addContentForms.find(form => form.lang === this.selectedLanguage);
    if (currentForm) {
      currentForm.form.patchValue({ author: '' });
    }
  }
}
