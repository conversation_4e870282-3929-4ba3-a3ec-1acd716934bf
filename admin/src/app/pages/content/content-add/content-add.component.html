<div class="panel">
  <button class="p-2 ml-auto block" (click)="goBack()">X</button>
  <div class="mb-5">
    <div class="mt-3 flex flex-wrap border-b border-white-light dark:border-[#191e3a]">
      @for(language of languages; track $index) {
        <a
          href="javascript:"
          class="-mb-[1px] block border border-transparent p-3.5 py-2 !outline-none transition duration-300 hover:text-primary dark:hover:border-b-black"
          [ngClass]="{ '!border-white-light !border-b-white  text-primary dark:!border-[#191e3a] dark:!border-b-black': selectedLanguage === language }"
          (click)="selectLanguage(language)"
        >
          {{language.toUpperCase()}}
        </a>
      }
    </div>
  </div>

  @for(item of addContentForms; track item.lang) {
    @if(selectedLanguage == item.lang) {
      <dialog #dialog>
        <div>
          {{message}}
        </div>
        <div class="mt-6 text-center">
          <button (click)="close(dialog)" class="cancel-button">OK</button>
        </div>
      </dialog>
      <form [formGroup]="item.form" class="space-y-5">
        <div *ngIf="selectedLanguage === 'ru'">
          <label>Категория</label>
          <div [ngClass]="{ 'has-error': !addValidationFormCategory() }" class="flex items-center">
            <select required (change)="onCategoryChange($event)" formControlName="category" class="form-multiselect">
              @for(category of contentService.categories(); track category.id) {
                <option [value]="category.id">{{category.title}}</option>
              }
            </select><span class="asterix">*</span>
            <span title="Категория" class="cursor-pointer mx-2">
              &#9432;
            </span>
          </div>
        </div>
        @if(slug) {
          <div>
            <label>Ссылка</label>
            <div [ngClass]="{ 'has-error': item.form.get('slug')?.invalid && item.form.get('slug')?.touched }" class="flex items-center">
              <input required type="text" formControlName="slug" placeholder="Ссылка" class="form-input"><span class="asterix">*</span>
              <span title="Ссылка" class="cursor-pointer mx-2">
                &#9432;
              </span>
            </div>
          </div>
          <div class="flex gap-3">
            <button class="btn btn-sm" (click)="copyLink(item)">Скопировать ссылку</button>
            <button class="btn btn-sm" (click)="redirect(item)">Перейти</button>
          </div>
        }
        <div>
          <label><input type="checkbox" class="form-checkbox" formControlName="active">Опубликовать</label>
        </div>
        <div>
          <label>Заголовок</label>
          <div [ngClass]="{ 'has-error': item.form.get('title')?.invalid && item.form.get('title')?.touched }" class="flex items-center">
            <input required type="text" formControlName="title" placeholder="Заголовок" class="form-input"><span class="asterix">*</span>
            <span title="Заголовок" class="cursor-pointer mx-2">
              &#9432;
            </span>
          </div>
        </div>

        <div>
          <label>SEO-заголовок</label>
          <div [ngClass]="{ 'has-error': item.form.get('seo_title')?.invalid && item.form.get('seo_title')?.touched }" class="flex items-center">
            <input required formControlName="seo_title" type="text" placeholder="SEO-заголовок" class="form-input"><span class="asterix">*</span>
            <span title="SEO-заголовок" class="cursor-pointer mx-2">
              &#9432;
            </span>
          </div>
        </div>
        <div>
          <label>SEO-описание</label>
          <div [ngClass]="{ 'has-error': item.form.get('seo_description')?.invalid && item.form.get('seo_description')?.touched }" class="flex items-center">
            <input required formControlName="seo_description" type="text" placeholder="SEO-описание" class="form-input"><span class="asterix">*</span>
            <span title="SEO-описание" class="cursor-pointer mx-2">
              &#9432;
            </span>
          </div>
        </div>
        <div>
          <label>Превью</label>
          <div [ngClass]="{ 'has-error': item.form.get('preview')?.invalid && item.form.get('preview')?.touched }" class="flex items-center">
            <input class="form-input" type="file"   accept="image/*" (change)="uploadPreview($event, item.form)"><span class="asterix">*</span>
            <span title="Превью" class="cursor-pointer mx-2">
              &#9432;
            </span>
          </div>
          @if(item.form.value.preview) {
            <PhotoPreview [disableRemoveBtn]="true" [items]="[item.form.value.preview]" [hasDescription]="false" (onItemRemoved)="item.form.patchValue({preview: null})"></PhotoPreview>
          }

        </div>
       <div>
          <label>Теги</label>
          <div [ngClass]="{ 'has-error': item.form.get('tags')?.invalid && item.form.get('tags')?.touched }" class="flex items-center">
            <ng-select
              [items]="tags"
              formControlName="tags"
              [multiple]="true"
              [clearable]="false"
              placeholder="Select an option"
              class="custom-multiselect"
              bindLabel="name"
              bindValue="id"
              required
            >
            </ng-select><span class="asterix">*</span>
            <span title="Теги" class="cursor-pointer mx-2">
              &#9432;
            </span>
          </div>
        </div>


        <div #editorElement class="editor-container" [ngClass]="{'editor-fullscreen': isEditorFullscreen}">
          <button type="button" class="fullscreen-toggle" (click)="toggleEditorFullscreen()">
            <span *ngIf="!isEditorFullscreen">
              <i class="icon-maximize"></i> На весь экран
            </span>
            <span *ngIf="isEditorFullscreen">
              <i class="icon-minimize"></i> Выйти из полноэкранного режима
            </span>
          </button>
          <ckeditor formControlName="content" [editor]="Editor" [config]="editorConfig" *ngIf="isLayoutReady" (ready)="onEditorReady($event)" />
        </div>

        <div>
          <label>Автор</label>
          <div class="flex items-center">
            <select formControlName="author" class="form-select">
              <option value="">Не выбрано</option>
              @for(author of authors; track author.id) {
                <option [value]="author.name">{{author.name}}</option>
              }
            </select>
            <button
              type="button"
              class="ml-2 px-2 py-1 text-gray-500 hover:text-red-500 cursor-pointer"
              (click)="clearAuthor()"
              title="Сбросить автора"
            >
              X
            </button>
          </div>
        </div>

         <div>
          <label>Телеграм</label>
          <div  class="flex items-center">
            <input formControlName="telegram" type="text" placeholder="Телеграм" class="form-input">
          </div>
        </div>
        <div>
          <label>Инстаграм</label>
          <div  class="flex items-center">
            <input formControlName="instagram" type="text" placeholder="Инстаграм" class="form-input">

          </div>
        </div>
        <div>
          <label>Телефон</label>
          <div class="flex items-center">
            <input formControlName="phone" type="text" placeholder="Телефон" class="form-input">
          </div>
        </div>
        <div>
          <label>E-mail</label>
          <div  class="flex items-center">
            <input formControlName="email" type="text" placeholder="E-mail" class="form-input">
          </div>
        </div>
        <div>
          <label>Ссылка на youtube</label>
          <div class="flex items-center">
            <input formControlName="youtube" type="text" placeholder="Ссылка на youtube" class="form-input">
          </div>
        </div>

        <div>
          <label>Кнопки</label>
          <button class="btn btn-primary btn-sm" (click)="showAddButtonForm()">Добавить</button>
          <table>
            <tbody>
              @for(button of buttons.value; track button.name; let i = $index) {
                <tr>
                  <td>{{button.name}}</td>
                  <td>
                    <div class="flex gap-2">
                      <button class="btn btn-primary btn-sm" (click)="editButton(i, button)">Редактировать</button>
                      <button class="btn btn-danger btn-sm" (click)="removeButton(i)">Удалить</button>
                    </div>
                  </td>
                </tr>
              }
            </tbody>
          </table>
        </div>

        <div>
          <label>Аудио</label>
          <button class="btn btn-primary btn-sm" (click)="showAddAudioForm()">Добавить</button>
          <table>
            <tbody>
              @for(audio of audiofiles.value; track $index; let i = $index) {
                <tr>
                  <td>{{audio.audio.title}}</td>
                  <td>
                    <div class="flex gap-2">
                      <button class="btn btn-primary btn-sm" (click)="editAudio(i, audio)">Редактировать</button>
                      <button class="btn btn-danger btn-sm" (click)="removeAudio(i)">Удалить</button>
                    </div>
                  </td>
                </tr>
              }
            </tbody>
          </table>
        </div>
      </form>
    }
  }

  <div class="fixed-buttons">
    @if(slug) {
      <button [disabled]="!addValidationFormSubmit()" (click)="addContentFormSubmit()" type="submit" class="btn btn-primary">Обновить</button>
      <button (click)="deleteWithCheckLinks()" type="submit" class="btn btn-danger">Удалить</button>
      <button (click)="back()" class="btn">Закрыть</button>
    } @else {
      <button [disabled]="!addValidationFormSubmit()" (click)="addContentFormSubmit()" type="submit" class="btn btn-primary">Создать</button>
      <button (click)="back()" class="btn">Закрыть</button>
    }
  </div>
</div>

<dialog #linkedContentDialog>
  <form [formGroup]="replaceLinkForm" class="space-y-3">
    <div style="text-align: center">
      Обнаружено {{linkedNum}} ссылок на данную статью.
      <br>
      Ссылки будут так же удалены.
    </div>
    <div>
      <label>Заменить на</label>
      <select formControlName="replace" class="form-select">
        <option value="">Не заменять</option>
        @for(item of allLinks; track item.id) {
          <option [value]="item.id">{{item.title}}</option>
        }
      </select>
    </div>
    <div>
      <button class="btn btn-danger" (click)="delete()">Удалить</button>
      <button class="btn btn-primary" (click)="closeModal()">Отмена</button>
    </div>
  </form>
</dialog>


<dialog #addButtonDialog>
  <form [formGroup]="buttonForm" class="space-y-3" style="min-width: 400px">
    <div>
      <label>Название</label>
      <input formControlName="name" type="text" class="form-input">
    </div>
    <div>
      <label>Ссылка</label>
      <input formControlName="link" type="text" class="form-input">
    </div>
    <div class="flex gap-2">
      <button class="btn btn-primary" (click)="addButton()">Сохранить</button>
      <button class="btn" (click)="closeAddButtonForm()">Закрыть</button>
    </div>
  </form>
</dialog>

<dialog #addAudioDialog>
  <form [formGroup]="audioForm" class="space-y-3" style="min-width: 400px">
    <div>
      <label>Выберите аудио из списка</label>

      <ng-select
        [items]="audiofiles_all"
        formControlName="audio"
        [multiple]="false"
        [clearable]="false"
        placeholder="Select an option"
        class="custom-multiselect"
        bindLabel="title"
        required
      >
      </ng-select>

    </div>
    <div class="flex gap-2">
      <button class="btn btn-primary" (click)="addAudio()">Сохранить</button>
      <button class="btn" (click)="closeAddAudioForm()">Закрыть</button>
    </div>
  </form>
</dialog>
