// Critical Path Optimizer - Inline script for immediate execution
(function() {
  'use strict';
  
  // Optimize resource loading priorities
  function optimizeResourcePriorities() {
    // Add fetchpriority to critical scripts
    const scripts = document.querySelectorAll('script[src]');
    scripts.forEach(script => {
      const src = script.src;
      if (src.includes('main') || src.includes('polyfills')) {
        script.setAttribute('fetchpriority', 'high');
      } else if (src.includes('chunk')) {
        script.setAttribute('fetchpriority', 'low');
      }
    });
    
    // Add fetchpriority to stylesheets
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
    stylesheets.forEach(link => {
      const href = link.href;
      if (href.includes('styles') || href.includes('main')) {
        link.setAttribute('fetchpriority', 'high');
      } else {
        link.setAttribute('fetchpriority', 'low');
      }
    });
  }
  
  // Preload critical resources
  function preloadCriticalResources() {
    const criticalResources = [
      { href: '/polyfills-FFHMD2TL.js', as: 'script', priority: 'high' },
      { href: '/main-3UKJ5DTP.js', as: 'script', priority: 'high' },
      { href: '/chunk-DCENPQ55.js', as: 'script', priority: 'high' },
      { href: '/styles.css', as: 'style', priority: 'high' }
    ];
    
    criticalResources.forEach(resource => {
      const existingLink = document.querySelector(`link[href="${resource.href}"]`);
      if (!existingLink) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource.href;
        link.as = resource.as;
        link.setAttribute('fetchpriority', resource.priority);
        document.head.appendChild(link);
      }
    });
  }
  
  // Optimize images for LCP
  function optimizeImages() {
    const images = document.querySelectorAll('img');
    images.forEach((img, index) => {
      if (index === 0) {
        // First image is likely LCP candidate
        img.setAttribute('fetchpriority', 'high');
        img.loading = 'eager';
      } else if (index < 3) {
        // First 3 images are above the fold
        img.setAttribute('fetchpriority', 'high');
      } else {
        // Other images can be lazy loaded
        img.loading = 'lazy';
        img.setAttribute('fetchpriority', 'low');
      }
    });
  }
  
  // Remove preloader when app is ready
  function removePreloader() {
    const preloader = document.getElementById('preloader');
    if (preloader) {
      // Wait for Angular to bootstrap
      const checkAngular = setInterval(() => {
        const appRoot = document.querySelector('app-root');
        if (appRoot && appRoot.children.length > 0) {
          preloader.classList.add('preloader-hidden');
          setTimeout(() => {
            preloader.remove();
          }, 300);
          clearInterval(checkAngular);
        }
      }, 100);
      
      // Fallback: remove after 5 seconds
      setTimeout(() => {
        if (preloader.parentNode) {
          preloader.classList.add('preloader-hidden');
          setTimeout(() => {
            preloader.remove();
          }, 300);
        }
        clearInterval(checkAngular);
      }, 5000);
    }
  }
  
  // Initialize optimizations
  function init() {
    // Run immediately
    optimizeResourcePriorities();
    preloadCriticalResources();
    
    // Run when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        optimizeImages();
        removePreloader();
      });
    } else {
      optimizeImages();
      removePreloader();
    }
    
    // Run when all resources are loaded
    window.addEventListener('load', () => {
      // Final optimization pass
      optimizeResourcePriorities();
    });
  }
  
  // Start optimization
  init();
})();
