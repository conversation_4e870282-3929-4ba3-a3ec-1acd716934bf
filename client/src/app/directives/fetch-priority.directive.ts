import { Directive, ElementRef, Input, OnInit, inject } from '@angular/core';

@Directive({
  selector: '[appFetchPriority]',
  standalone: true
})
export class FetchPriorityDirective implements OnInit {
  @Input() appFetchPriority: 'high' | 'low' | 'auto' = 'auto';
  @Input() isLCP: boolean = false;

  private elementRef = inject(ElementRef);

  ngOnInit(): void {
    const element = this.elementRef.nativeElement;
    
    if (this.isLCP) {
      // Mark as LCP element with highest priority
      element.setAttribute('fetchpriority', 'high');
      if (element.tagName === 'IMG') {
        element.loading = 'eager';
      }
    } else {
      element.setAttribute('fetchpriority', this.appFetchPriority);
    }

    // Add intersection observer for lazy loading optimization
    if (this.appFetchPriority === 'low' && 'IntersectionObserver' in window) {
      this.setupLazyLoading(element);
    }
  }

  private setupLazyLoading(element: HTMLElement): void {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Element is about to be visible, increase priority
          element.setAttribute('fetchpriority', 'high');
          observer.unobserve(element);
        }
      });
    }, {
      rootMargin: '50px' // Start loading 50px before element comes into view
    });

    observer.observe(element);
  }
}
