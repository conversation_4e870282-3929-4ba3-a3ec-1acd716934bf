import { Component, Input, OnInit, ElementRef, inject, PLATFORM_ID } from '@angular/core';
import { NgOptimizedImage, isPlatformBrowser } from '@angular/common';
import { FetchPriorityDirective } from '../../directives/fetch-priority.directive';

@Component({
  selector: 'app-optimized-image',
  standalone: true,
  imports: [NgOptimizedImage, FetchPriorityDirective],
  template: `
    <img 
      [ngSrc]="src" 
      [alt]="alt"
      [width]="width"
      [height]="height"
      [priority]="isAboveFold"
      [appFetchPriority]="fetchPriority"
      [isLCP]="isLCP"
      [loading]="loading"
      [sizes]="sizes"
      (load)="onImageLoad()"
      (error)="onImageError()"
    />
  `
})
export class OptimizedImageComponent implements OnInit {
  @Input() src!: string;
  @Input() alt!: string;
  @Input() width!: number;
  @Input() height!: number;
  @Input() isAboveFold: boolean = false;
  @Input() isLCP: boolean = false;
  @Input() sizes?: string;

  private platformId = inject(PLATFORM_ID);
  private elementRef = inject(ElementRef);

  fetchPriority: 'high' | 'low' | 'auto' = 'auto';
  loading: 'eager' | 'lazy' = 'lazy';

  ngOnInit(): void {
    this.determinePriority();
  }

  private determinePriority(): void {
    if (this.isLCP || this.isAboveFold) {
      this.fetchPriority = 'high';
      this.loading = 'eager';
    } else if (this.isInViewport()) {
      this.fetchPriority = 'high';
      this.loading = 'eager';
    } else {
      this.fetchPriority = 'low';
      this.loading = 'lazy';
    }
  }

  private isInViewport(): boolean {
    if (!isPlatformBrowser(this.platformId)) {
      return false;
    }

    const rect = this.elementRef.nativeElement.getBoundingClientRect();
    const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
    
    // Consider image in viewport if it's within 100px of the visible area
    return rect.top < viewportHeight + 100;
  }

  onImageLoad(): void {
    // Image loaded successfully
    if (isPlatformBrowser(this.platformId) && this.isLCP) {
      // Report LCP if this is the LCP element
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          console.log('LCP:', lastEntry.startTime);
        });
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
      }
    }
  }

  onImageError(): void {
    console.warn('Failed to load image:', this.src);
    // Could implement fallback image logic here
  }
}
