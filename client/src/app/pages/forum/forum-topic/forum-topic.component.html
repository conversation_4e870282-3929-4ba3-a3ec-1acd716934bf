<div *ngIf="topic">
  <div class="middle_stripe">
    <breadcrumb></breadcrumb>
    <div class="wrapper_line">
      <h1 class="dec_head-title_">Техники дыхательных практик</h1>
      <div class="cat_wrap">
        <div class="forum-category">
          <div class="forum-header">
            <div class="topic-user">
              <div class="topic-user__avatar">
                <img *ngIf="topic.user.avatar" [src]="environment.serverUrl + '/upload/' + topic.user.avatar.name"
                  alt="">
                <span *ngIf="!topic.user.avatar">{{userInitials}}</span>
              </div>
            </div>
            <div class="topic-info">
              <div class="topic-user_">{{topic.user.firstName}} {{topic.user.lastName}}</div>
              <!-- <div class="topic-title">{{topic.name}}</div> -->
              <div class="topic-user_">
                {{formatDate(topic.createdAt)}}
              </div>
            </div>
          </div>
          <div class="topic-content">
            <span>{{topic.content}}</span>
            <div class="topic-images" *ngIf="topic.images.length">
              @for(image of topic.images; track image.id) {
              <div>
                <img [src]="environment.serverUrl + '/upload/' + image.name" alt="">
              </div>
              }
            </div>
          </div>
          <div class="topic-comment__footer" *ngIf="profileService.isAuth">
            <!-- <div class="topic-comment__like" [ngClass]="{'is-liked': topic?.isLiked}" (click)="likeTopic(topic.id)">
              <svg _ngcontent-ng-c3680555871="" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <path _ngcontent-ng-c3680555871="" d="m0 0h24v24h-24z" fill="#fff" opacity="0"></path>
                <path _ngcontent-ng-c3680555871=""
                  d="m12 21a1 1 0 0 1 -.71-.29l-7.77-7.78a5.26 5.26 0 0 1 0-7.4 5.24 5.24 0 0 1 7.4 0l1.08 1.08 1.08-1.08a5.24 5.24 0 0 1 7.4 0 5.26 5.26 0 0 1 0 7.4l-7.77 7.78a1 1 0 0 1 -.71.29z"
                  fill="transparent" stroke="black"></path>
              </svg>
              <span>{{topic.likes.length}}</span>
            </div> -->
            <button class="flex items-center icons_w like_hov btn_like" [ngClass]="{'is-liked': topic?.isLiked}"
              (click)="likeTopic(topic.id)">
              <div class="flex items-center icon-wrap like_w ">
                <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                    fill="var(--font-color1)" />
                </svg>
                <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                    fill="var(--text-color)" />
                </svg>
                <div class="on_hov">
                  мне нравится
                </div>
              </div>
              <span class="ml-2 pt-[2px]">{{topic.likes.length}}</span>
            </button>
            <button class="icons_w fav_hov btn-favourite" [ngClass]="{'in-favourites': topic?.isLiked}"
              (click)="favoriteTopic(topic.id)">
              <div class="icon-wrap star_w">
                <svg class="emty_f" width="25" height="24" viewBox="0 0 25 24" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                    fill="var(--font-color1)" />
                </svg>
                <svg class="emty_f_hover" width="25" height="24" viewBox="0 0 25 24" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                    fill="var(--text-color)" />
                </svg>
                <div class="on_hov">
                  добавить в избранное
                </div>
                <!-- <span>{{topic.favorites.length}}</span> -->
              </div>
            </button>
            <div class="flex items-center cursor-pointer icons_w t_p fav_hov">
              <div class="icon-wrap star_w">
                <svg class="emty_f" width="20" height="20" viewBox="0 0 20 20" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M5.0748 15.1053L5.57859 14.9053C5.80948 14.8105 5.9984 14.6737 6.14534 14.4737C6.51268 13.9895 6.89052 13.5053 7.25786 13.0316L7.63569 12.5474C8.18146 11.8421 8.71673 11.1474 9.26249 10.4421C10.5114 8.82106 11.8129 7.13685 13.0933 5.47369C14.0064 4.28422 13.9645 2.70527 12.9884 1.64211C12.663 1.28421 12.2642 0.989475 11.8759 0.705264C11.8024 0.652632 11.7184 0.589474 11.645 0.536843C11.1622 0.178948 10.5954 0 9.96568 0H9.94469C8.95812 0.0105263 8.13948 0.421053 7.53074 1.21053C5.15877 4.28422 2.84977 7.28422 0.530272 10.3053C0.404326 10.4632 0.320363 10.6948 0.309867 10.8948C0.25739 11.579 0.225903 12.2842 0.183922 12.9579V13.0527C0.173426 13.3053 0.152435 13.5474 0.14194 13.8C0.131444 14.0105 0.120949 14.2105 0.110453 14.4211C0.0894626 14.9474 0.0579762 15.4842 0.00549892 16.0105C-0.0259874 16.3158 0.0789671 16.5895 0.288876 16.7369C0.393831 16.8211 0.530272 16.8632 0.677208 16.8632C0.792658 16.8632 0.918603 16.8316 1.04455 16.779C2.36698 16.2105 3.74188 15.6527 5.0748 15.1053ZM1.87369 10.7053L7.3943 3.53685L10.8788 6.18948L5.35818 13.3369L1.87369 10.7053ZM10.8368 1.57895C11.1622 1.78948 11.5085 2.04211 11.8759 2.36842C12.4846 2.89474 12.6105 3.84211 12.1487 4.52632C12.0333 4.70527 11.8968 4.87369 11.7499 5.06316L11.7289 5.09474C11.7079 5.1158 11.6974 5.13685 11.6764 5.1579L8.20245 2.49474C8.24443 2.44211 8.27592 2.4 8.3179 2.34737C8.59078 1.97895 8.85317 1.62106 9.28348 1.45263C9.82924 1.23158 10.354 1.27369 10.8368 1.57895ZM4.1617 14.0632L1.36991 15.2105L1.54833 12.0948L4.1617 14.0632Z"
                    fill="var(--font-color1)" />
                  <path
                    d="M19.5593 17.0316C19.3704 16.9369 14.7629 14.7263 9.91399 17.0316C5.06509 19.3263 3.1969 18.3263 2.10538 17.2C1.80101 16.8842 1.30772 16.8737 0.992857 17.179C0.677994 17.4842 0.667498 17.979 0.971867 18.2948C2.06339 19.4316 3.39632 20 5.03361 20C6.58693 20 8.42364 19.4842 10.5857 18.4632C14.7419 16.4948 18.8246 18.4421 18.8666 18.4632C19.2549 18.6527 19.7272 18.4948 19.9162 18.1053C20.1156 17.7053 19.9476 17.2316 19.5593 17.0316Z"
                    fill="var(--font-color1)" />
                </svg>
                <svg class="emty_f_hover" width="20" height="20" viewBox="0 0 20 20" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M5.0748 15.1053L5.57859 14.9053C5.80948 14.8105 5.9984 14.6737 6.14534 14.4737C6.51268 13.9895 6.89052 13.5053 7.25786 13.0316L7.63569 12.5474C8.18146 11.8421 8.71673 11.1474 9.26249 10.4421C10.5114 8.82106 11.8129 7.13685 13.0933 5.47369C14.0064 4.28422 13.9645 2.70527 12.9884 1.64211C12.663 1.28421 12.2642 0.989475 11.8759 0.705264C11.8024 0.652632 11.7184 0.589474 11.645 0.536843C11.1622 0.178948 10.5954 0 9.96568 0H9.94469C8.95812 0.0105263 8.13948 0.421053 7.53074 1.21053C5.15877 4.28422 2.84977 7.28422 0.530272 10.3053C0.404326 10.4632 0.320363 10.6948 0.309867 10.8948C0.25739 11.579 0.225903 12.2842 0.183922 12.9579V13.0527C0.173426 13.3053 0.152435 13.5474 0.14194 13.8C0.131444 14.0105 0.120949 14.2105 0.110453 14.4211C0.0894626 14.9474 0.0579762 15.4842 0.00549892 16.0105C-0.0259874 16.3158 0.0789671 16.5895 0.288876 16.7369C0.393831 16.8211 0.530272 16.8632 0.677208 16.8632C0.792658 16.8632 0.918603 16.8316 1.04455 16.779C2.36698 16.2105 3.74188 15.6527 5.0748 15.1053ZM1.87369 10.7053L7.3943 3.53685L10.8788 6.18948L5.35818 13.3369L1.87369 10.7053ZM10.8368 1.57895C11.1622 1.78948 11.5085 2.04211 11.8759 2.36842C12.4846 2.89474 12.6105 3.84211 12.1487 4.52632C12.0333 4.70527 11.8968 4.87369 11.7499 5.06316L11.7289 5.09474C11.7079 5.1158 11.6974 5.13685 11.6764 5.1579L8.20245 2.49474C8.24443 2.44211 8.27592 2.4 8.3179 2.34737C8.59078 1.97895 8.85317 1.62106 9.28348 1.45263C9.82924 1.23158 10.354 1.27369 10.8368 1.57895ZM4.1617 14.0632L1.36991 15.2105L1.54833 12.0948L4.1617 14.0632Z"
                    fill="var(--text-color)" />
                  <path
                    d="M19.5593 17.0316C19.3704 16.9369 14.7629 14.7263 9.91399 17.0316C5.06509 19.3263 3.1969 18.3263 2.10538 17.2C1.80101 16.8842 1.30772 16.8737 0.992857 17.179C0.677994 17.4842 0.667498 17.979 0.971867 18.2948C2.06339 19.4316 3.39632 20 5.03361 20C6.58693 20 8.42364 19.4842 10.5857 18.4632C14.7419 16.4948 18.8246 18.4421 18.8666 18.4632C19.2549 18.6527 19.7272 18.4948 19.9162 18.1053C20.1156 17.7053 19.9476 17.2316 19.5593 17.0316Z"
                    fill="var(--text-color)" />
                </svg>
              </div> <!-- TODO uncomment if need tooltips-->
              <!-- <div class="on_hov">
                      редактировать
                    </div> -->
            </div>
            <a class="comment-reply" (click)="$event.stopPropagation();">Ответить</a>
            <!-- <div class="topic-comment__like fsvv" [ngClass]="{'is-liked': topic?.isLiked}" (click)="favoriteTopic(topic.id)"
              style="margin-left: 10px">
              <svg *ngIf="topic.isFavorite" width="800px" height="800px" viewBox="0 0 24 24" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M5.75 1C6.16421 1 6.5 1.33579 6.5 1.75V3.6L8.22067 3.25587C9.8712 2.92576 11.5821 3.08284 13.1449 3.70797L13.3486 3.78943C14.9097 4.41389 16.628 4.53051 18.2592 4.1227C19.0165 3.93339 19.75 4.50613 19.75 5.28669V12.6537C19.75 13.298 19.3115 13.8596 18.6864 14.0159L18.472 14.0695C16.7024 14.5119 14.8385 14.3854 13.1449 13.708C11.5821 13.0828 9.8712 12.9258 8.22067 13.2559L6.5 13.6V21.75C6.5 22.1642 6.16421 22.5 5.75 22.5C5.33579 22.5 5 22.1642 5 21.75V1.75C5 1.33579 5.33579 1 5.75 1Z"
                  fill="#1C274C" />
              </svg>
              <svg *ngIf="!topic.isFavorite" width="800px" height="800px" viewBox="0 0 24 24" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path d="M5 22V14V4V2" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round" />
                <path opacity="0.5"
                  d="M5 14L7.47067 13.5059C9.1212 13.1758 10.8321 13.3329 12.3949 13.958C14.0885 14.6354 15.9524 14.7619 17.722 14.3195L17.9364 14.2659C18.5615 14.1097 19 13.548 19 12.9038V5.53673C19 4.75617 18.2665 4.18343 17.5092 4.37274C15.878 4.78055 14.1597 4.66393 12.5986 4.03947L12.3949 3.95801C10.8321 3.33288 9.1212 3.1758 7.47067 3.50591L5 4.00004"
                  stroke="#1C274C" stroke-width="1.5" stroke-linecap="round" />
              </svg>
              <span>{{topic.favorites.length}}</span>
            </div> -->
          </div>
          <div class="div_sep"></div>
        </div>
        <div>
          <div *ngIf="topic.comments && topic.comments.length">
            @for(comment of topic.comments; track comment.id) {
            <div class="topic-comment">
              <div class="forum-header">
                <div class="topic-user">
                  <div class="topic-user__avatar">
                    <img *ngIf="comment.user.avatar"
                      [src]="environment.serverUrl + '/upload/' + comment.user.avatar.name" alt="">
                    <span *ngIf="!comment.user.avatar">{{comment.user.firstName[0]}}{{comment.user.lastName[0]}}</span>
                  </div>
                </div>
                <div class="topic-info">
                  <div class="topic-user_">{{comment.user.firstName}} {{comment.user.lastName}}</div>
                  <!-- <div class="topic-title"></div> -->
                  <div class="topic-user_">
                    {{formatDate(comment.createdAt)}}
                  </div>
                </div>
              </div>
              <div class="topic-comment__reply" *ngIf="comment.reply.length">
                <div class="topic-comment">
                  <div class="forum-header">
                    <div class="topic-user justify-between w-full">
                      <div class="topic-user__avatar">
                        <img *ngIf="comment.reply[0]['user']['avatar']"
                          [src]="environment.serverUrl + '/upload/' + comment.reply[0]['user']['avatar']['name']"
                          alt="">
                      </div>
                      <div class="topic-info">
                        <div class="topic-user_">{{comment.reply[0]['user']['firstName']}}
                          {{comment.reply[0]['lastName']}}</div>
                        <div class="topic-title"></div>
                        <div class="topic-user_">
                          {{formatDate(comment.reply[0]['createdAt'])}}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="topic-comment__text" [innerHTML]="comment.reply[0]['comment']"></div>
                </div>
              </div>
              <div class="topic-content">
                <span [innerHTML]="comment.comment"></span>
              </div>
              <div class="topic-comment__footer" *ngIf="profileService.isAuth">
                <button class="flex items-center icons_w like_hov btn_like" [ngClass]="{'is-liked': comment?.isLiked}"
                  (click)="likeComment(comment.id)">
                  <div class="flex items-center icon-wrap like_w ">
                    <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                        fill="var(--font-color1)" />
                    </svg>
                    <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                        fill="var(--text-color)" />
                    </svg>
                    <div class="on_hov">
                      мне нравится
                    </div>
                  </div>
                  <span class="ml-2 pt-[2px]">{{topic.likes.length}}</span>
                </button>
                <button class="icons_w fav_hov btn-favourite" [ngClass]="{'in-favourites': comment.isFavorite}"
                  (click)="favoriteComment(comment.id)">
                  <div class="icon-wrap star_w">
                    <svg class="emty_f" width="25" height="24" viewBox="0 0 25 24" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                        fill="var(--font-color1)" />
                    </svg>
                    <svg class="emty_f_hover" width="25" height="24" viewBox="0 0 25 24" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                        fill="var(--text-color)" />
                    </svg>
                    <div class="on_hov">
                      добавить в избранное
                    </div>
                    <!-- <span>{{comment.favorites.length}}</span> -->
                  </div>
                </button>
                <div class="flex items-center cursor-pointer icons_w t_p fav_hov">
                  <div class="icon-wrap star_w">
                    <svg class="emty_f" width="20" height="20" viewBox="0 0 20 20" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M5.0748 15.1053L5.57859 14.9053C5.80948 14.8105 5.9984 14.6737 6.14534 14.4737C6.51268 13.9895 6.89052 13.5053 7.25786 13.0316L7.63569 12.5474C8.18146 11.8421 8.71673 11.1474 9.26249 10.4421C10.5114 8.82106 11.8129 7.13685 13.0933 5.47369C14.0064 4.28422 13.9645 2.70527 12.9884 1.64211C12.663 1.28421 12.2642 0.989475 11.8759 0.705264C11.8024 0.652632 11.7184 0.589474 11.645 0.536843C11.1622 0.178948 10.5954 0 9.96568 0H9.94469C8.95812 0.0105263 8.13948 0.421053 7.53074 1.21053C5.15877 4.28422 2.84977 7.28422 0.530272 10.3053C0.404326 10.4632 0.320363 10.6948 0.309867 10.8948C0.25739 11.579 0.225903 12.2842 0.183922 12.9579V13.0527C0.173426 13.3053 0.152435 13.5474 0.14194 13.8C0.131444 14.0105 0.120949 14.2105 0.110453 14.4211C0.0894626 14.9474 0.0579762 15.4842 0.00549892 16.0105C-0.0259874 16.3158 0.0789671 16.5895 0.288876 16.7369C0.393831 16.8211 0.530272 16.8632 0.677208 16.8632C0.792658 16.8632 0.918603 16.8316 1.04455 16.779C2.36698 16.2105 3.74188 15.6527 5.0748 15.1053ZM1.87369 10.7053L7.3943 3.53685L10.8788 6.18948L5.35818 13.3369L1.87369 10.7053ZM10.8368 1.57895C11.1622 1.78948 11.5085 2.04211 11.8759 2.36842C12.4846 2.89474 12.6105 3.84211 12.1487 4.52632C12.0333 4.70527 11.8968 4.87369 11.7499 5.06316L11.7289 5.09474C11.7079 5.1158 11.6974 5.13685 11.6764 5.1579L8.20245 2.49474C8.24443 2.44211 8.27592 2.4 8.3179 2.34737C8.59078 1.97895 8.85317 1.62106 9.28348 1.45263C9.82924 1.23158 10.354 1.27369 10.8368 1.57895ZM4.1617 14.0632L1.36991 15.2105L1.54833 12.0948L4.1617 14.0632Z"
                        fill="var(--font-color1)" />
                      <path
                        d="M19.5593 17.0316C19.3704 16.9369 14.7629 14.7263 9.91399 17.0316C5.06509 19.3263 3.1969 18.3263 2.10538 17.2C1.80101 16.8842 1.30772 16.8737 0.992857 17.179C0.677994 17.4842 0.667498 17.979 0.971867 18.2948C2.06339 19.4316 3.39632 20 5.03361 20C6.58693 20 8.42364 19.4842 10.5857 18.4632C14.7419 16.4948 18.8246 18.4421 18.8666 18.4632C19.2549 18.6527 19.7272 18.4948 19.9162 18.1053C20.1156 17.7053 19.9476 17.2316 19.5593 17.0316Z"
                        fill="var(--font-color1)" />
                    </svg>
                    <svg class="emty_f_hover" width="20" height="20" viewBox="0 0 20 20" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M5.0748 15.1053L5.57859 14.9053C5.80948 14.8105 5.9984 14.6737 6.14534 14.4737C6.51268 13.9895 6.89052 13.5053 7.25786 13.0316L7.63569 12.5474C8.18146 11.8421 8.71673 11.1474 9.26249 10.4421C10.5114 8.82106 11.8129 7.13685 13.0933 5.47369C14.0064 4.28422 13.9645 2.70527 12.9884 1.64211C12.663 1.28421 12.2642 0.989475 11.8759 0.705264C11.8024 0.652632 11.7184 0.589474 11.645 0.536843C11.1622 0.178948 10.5954 0 9.96568 0H9.94469C8.95812 0.0105263 8.13948 0.421053 7.53074 1.21053C5.15877 4.28422 2.84977 7.28422 0.530272 10.3053C0.404326 10.4632 0.320363 10.6948 0.309867 10.8948C0.25739 11.579 0.225903 12.2842 0.183922 12.9579V13.0527C0.173426 13.3053 0.152435 13.5474 0.14194 13.8C0.131444 14.0105 0.120949 14.2105 0.110453 14.4211C0.0894626 14.9474 0.0579762 15.4842 0.00549892 16.0105C-0.0259874 16.3158 0.0789671 16.5895 0.288876 16.7369C0.393831 16.8211 0.530272 16.8632 0.677208 16.8632C0.792658 16.8632 0.918603 16.8316 1.04455 16.779C2.36698 16.2105 3.74188 15.6527 5.0748 15.1053ZM1.87369 10.7053L7.3943 3.53685L10.8788 6.18948L5.35818 13.3369L1.87369 10.7053ZM10.8368 1.57895C11.1622 1.78948 11.5085 2.04211 11.8759 2.36842C12.4846 2.89474 12.6105 3.84211 12.1487 4.52632C12.0333 4.70527 11.8968 4.87369 11.7499 5.06316L11.7289 5.09474C11.7079 5.1158 11.6974 5.13685 11.6764 5.1579L8.20245 2.49474C8.24443 2.44211 8.27592 2.4 8.3179 2.34737C8.59078 1.97895 8.85317 1.62106 9.28348 1.45263C9.82924 1.23158 10.354 1.27369 10.8368 1.57895ZM4.1617 14.0632L1.36991 15.2105L1.54833 12.0948L4.1617 14.0632Z"
                        fill="var(--text-color)" />
                      <path
                        d="M19.5593 17.0316C19.3704 16.9369 14.7629 14.7263 9.91399 17.0316C5.06509 19.3263 3.1969 18.3263 2.10538 17.2C1.80101 16.8842 1.30772 16.8737 0.992857 17.179C0.677994 17.4842 0.667498 17.979 0.971867 18.2948C2.06339 19.4316 3.39632 20 5.03361 20C6.58693 20 8.42364 19.4842 10.5857 18.4632C14.7419 16.4948 18.8246 18.4421 18.8666 18.4632C19.2549 18.6527 19.7272 18.4948 19.9162 18.1053C20.1156 17.7053 19.9476 17.2316 19.5593 17.0316Z"
                        fill="var(--text-color)" />
                    </svg>
                  </div> <!-- TODO uncomment if need tooltips-->
                  <!-- <div class="on_hov">
                      редактировать
                    </div> -->
                </div>
                <a class="comment-reply" (click)="$event.stopPropagation(); replyComment(comment)">Ответить</a>
                <!-- <div class="topic-actions">
                  <a class="comment-reply" (click)="$event.stopPropagation(); replyComment(comment)">Ответить</a>
                  <a *ngIf="comment?.access && isEditableComment(comment.access)" class="comment-edit"
                    (click)="$event.stopPropagation(); editComment(comment)">Редактировать</a>
                  <a *ngIf="comment?.access && isRemovableComment(comment.access)" class="comment-remove"
                    (click)="$event.stopPropagation(); removeComment(comment.id)">Удалить</a>
                </div> -->
              </div>
              <div class="div_sep"></div>
            </div>
            }
          </div>
          <form [formGroup]="commentForm" class="comment-form" *ngIf="profileService.isAuth">
            <div class="reply" *ngIf="commentForm.value.reply">
              <div class="topic-comment">
                <div class="forum-header">
                  <div class="topic-user  justify-between w-full">
                    <div class="topic-user__avatar"></div>
                    <div class="topic-info">
                      <div class="topic-user_">{{commentForm.value.reply['user']['firstName']}}
                        {{commentForm.value.reply['user']['lastName']}}</div>
                      <div class="topic-title"></div>
                      <div class="topic-user_">
                        {{formatDate(commentForm.value.reply['createdAt'])}}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="topic-content">
                  <span [innerHTML]="commentForm.value.reply['comment']"></span>
                </div>
                <div class="topic-comment__footer" *ngIf="profileService.isAuth">
                  <button class="flex items-center icons_w like_hov btn_like" [ngClass]="{'is-liked': true}"
                    (click)="null">
                    <div class="flex items-center icon-wrap like_w ">
                      <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                          fill="var(--font-color1)" />
                      </svg>
                      <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                          fill="var(--text-color)" />
                      </svg>
                      <div class="on_hov">
                        мне нравится
                      </div>
                    </div>
                    <span class="ml-2 pt-[2px]">12</span>
                  </button>
                  <button class="icons_w fav_hov btn-favourite" [ngClass]="{'in-favourites': false}" (click)="null">
                    <div class="icon-wrap star_w">
                      <svg class="emty_f" width="25" height="24" viewBox="0 0 25 24" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                          fill="var(--font-color1)" />
                      </svg>
                      <svg class="emty_f_hover" width="25" height="24" viewBox="0 0 25 24" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                          fill="var(--text-color)" />
                      </svg>
                      <div class="on_hov">
                        добавить в избранное
                      </div>
                      <!-- <span>{{comment.favorites.length}}</span> -->
                    </div>
                  </button>
                  <div class="flex items-center cursor-pointer icons_w t_p fav_hov">
                    <div class="icon-wrap star_w">
                      <svg class="emty_f" width="20" height="20" viewBox="0 0 20 20" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M5.0748 15.1053L5.57859 14.9053C5.80948 14.8105 5.9984 14.6737 6.14534 14.4737C6.51268 13.9895 6.89052 13.5053 7.25786 13.0316L7.63569 12.5474C8.18146 11.8421 8.71673 11.1474 9.26249 10.4421C10.5114 8.82106 11.8129 7.13685 13.0933 5.47369C14.0064 4.28422 13.9645 2.70527 12.9884 1.64211C12.663 1.28421 12.2642 0.989475 11.8759 0.705264C11.8024 0.652632 11.7184 0.589474 11.645 0.536843C11.1622 0.178948 10.5954 0 9.96568 0H9.94469C8.95812 0.0105263 8.13948 0.421053 7.53074 1.21053C5.15877 4.28422 2.84977 7.28422 0.530272 10.3053C0.404326 10.4632 0.320363 10.6948 0.309867 10.8948C0.25739 11.579 0.225903 12.2842 0.183922 12.9579V13.0527C0.173426 13.3053 0.152435 13.5474 0.14194 13.8C0.131444 14.0105 0.120949 14.2105 0.110453 14.4211C0.0894626 14.9474 0.0579762 15.4842 0.00549892 16.0105C-0.0259874 16.3158 0.0789671 16.5895 0.288876 16.7369C0.393831 16.8211 0.530272 16.8632 0.677208 16.8632C0.792658 16.8632 0.918603 16.8316 1.04455 16.779C2.36698 16.2105 3.74188 15.6527 5.0748 15.1053ZM1.87369 10.7053L7.3943 3.53685L10.8788 6.18948L5.35818 13.3369L1.87369 10.7053ZM10.8368 1.57895C11.1622 1.78948 11.5085 2.04211 11.8759 2.36842C12.4846 2.89474 12.6105 3.84211 12.1487 4.52632C12.0333 4.70527 11.8968 4.87369 11.7499 5.06316L11.7289 5.09474C11.7079 5.1158 11.6974 5.13685 11.6764 5.1579L8.20245 2.49474C8.24443 2.44211 8.27592 2.4 8.3179 2.34737C8.59078 1.97895 8.85317 1.62106 9.28348 1.45263C9.82924 1.23158 10.354 1.27369 10.8368 1.57895ZM4.1617 14.0632L1.36991 15.2105L1.54833 12.0948L4.1617 14.0632Z"
                          fill="var(--font-color1)" />
                        <path
                          d="M19.5593 17.0316C19.3704 16.9369 14.7629 14.7263 9.91399 17.0316C5.06509 19.3263 3.1969 18.3263 2.10538 17.2C1.80101 16.8842 1.30772 16.8737 0.992857 17.179C0.677994 17.4842 0.667498 17.979 0.971867 18.2948C2.06339 19.4316 3.39632 20 5.03361 20C6.58693 20 8.42364 19.4842 10.5857 18.4632C14.7419 16.4948 18.8246 18.4421 18.8666 18.4632C19.2549 18.6527 19.7272 18.4948 19.9162 18.1053C20.1156 17.7053 19.9476 17.2316 19.5593 17.0316Z"
                          fill="var(--font-color1)" />
                      </svg>
                      <svg class="emty_f_hover" width="20" height="20" viewBox="0 0 20 20" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M5.0748 15.1053L5.57859 14.9053C5.80948 14.8105 5.9984 14.6737 6.14534 14.4737C6.51268 13.9895 6.89052 13.5053 7.25786 13.0316L7.63569 12.5474C8.18146 11.8421 8.71673 11.1474 9.26249 10.4421C10.5114 8.82106 11.8129 7.13685 13.0933 5.47369C14.0064 4.28422 13.9645 2.70527 12.9884 1.64211C12.663 1.28421 12.2642 0.989475 11.8759 0.705264C11.8024 0.652632 11.7184 0.589474 11.645 0.536843C11.1622 0.178948 10.5954 0 9.96568 0H9.94469C8.95812 0.0105263 8.13948 0.421053 7.53074 1.21053C5.15877 4.28422 2.84977 7.28422 0.530272 10.3053C0.404326 10.4632 0.320363 10.6948 0.309867 10.8948C0.25739 11.579 0.225903 12.2842 0.183922 12.9579V13.0527C0.173426 13.3053 0.152435 13.5474 0.14194 13.8C0.131444 14.0105 0.120949 14.2105 0.110453 14.4211C0.0894626 14.9474 0.0579762 15.4842 0.00549892 16.0105C-0.0259874 16.3158 0.0789671 16.5895 0.288876 16.7369C0.393831 16.8211 0.530272 16.8632 0.677208 16.8632C0.792658 16.8632 0.918603 16.8316 1.04455 16.779C2.36698 16.2105 3.74188 15.6527 5.0748 15.1053ZM1.87369 10.7053L7.3943 3.53685L10.8788 6.18948L5.35818 13.3369L1.87369 10.7053ZM10.8368 1.57895C11.1622 1.78948 11.5085 2.04211 11.8759 2.36842C12.4846 2.89474 12.6105 3.84211 12.1487 4.52632C12.0333 4.70527 11.8968 4.87369 11.7499 5.06316L11.7289 5.09474C11.7079 5.1158 11.6974 5.13685 11.6764 5.1579L8.20245 2.49474C8.24443 2.44211 8.27592 2.4 8.3179 2.34737C8.59078 1.97895 8.85317 1.62106 9.28348 1.45263C9.82924 1.23158 10.354 1.27369 10.8368 1.57895ZM4.1617 14.0632L1.36991 15.2105L1.54833 12.0948L4.1617 14.0632Z"
                          fill="var(--text-color)" />
                        <path
                          d="M19.5593 17.0316C19.3704 16.9369 14.7629 14.7263 9.91399 17.0316C5.06509 19.3263 3.1969 18.3263 2.10538 17.2C1.80101 16.8842 1.30772 16.8737 0.992857 17.179C0.677994 17.4842 0.667498 17.979 0.971867 18.2948C2.06339 19.4316 3.39632 20 5.03361 20C6.58693 20 8.42364 19.4842 10.5857 18.4632C14.7419 16.4948 18.8246 18.4421 18.8666 18.4632C19.2549 18.6527 19.7272 18.4948 19.9162 18.1053C20.1156 17.7053 19.9476 17.2316 19.5593 17.0316Z"
                          fill="var(--text-color)" />
                      </svg>
                    </div> <!-- TODO uncomment if need tooltips-->
                    <!-- <div class="on_hov">
                      редактировать
                    </div> -->
                  </div>
                  <a class="comment-reply" (click)="$event.stopPropagation()">Ответить</a>
                </div>
              </div>
            </div>
            <div class="reply___" *ngIf="!commentForm.value.reply">Ваш ответ:</div>
            <div class="reply___" *ngIf="commentForm.value.reply">Ваш ответ на комментарий:
              <a class="cancel-reply" (click)="cancelReply()">Отменить</a>
            </div>
            <div class="simple-editor">
              <div class="editor-toolbar relative">
                <button type="button" [ngClass]="{'active-format': isBoldActive}" (click)="toggleFormat('bold')"
                  title="Жирный">B</button>
                <button type="button" [ngClass]="{'active-format': isItalicActive}" class="itc"
                  (click)="toggleFormat('italic')" title="Курсив">I</button>
                <button type="button" [ngClass]="{'active-format': isUnderlineActive}"
                  (click)="toggleFormat('underline')" class="und" title="Подчеркнутый">U</button>
                <button type="button" (click)="formatText('link')" title="Ссылка"><svg width="20" height="20"
                    viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M0 14.8872C0.052139 14.6266 0.0938502 14.366 0.161631 14.1106C0.364973 13.3445 0.73516 12.6617 1.29305 12.0937C2.71644 10.6656 4.1242 9.22196 5.57366 7.82521C7.66443 5.81867 11.189 6.29815 12.6958 8.7529C12.7167 8.78939 12.7428 8.83108 12.7428 8.83108C12.4664 9.12815 12.2266 9.44607 11.9294 9.69102C11.481 10.0663 10.8501 10.0037 10.4017 9.58158C9.92205 9.13336 9.35895 8.90404 8.70721 8.91447C8.09197 8.92489 7.54973 9.13858 7.10655 9.57636C5.73529 10.9419 4.35882 12.3073 2.99799 13.6885C2.1012 14.5953 2.12206 16.0598 3.01363 16.9563C3.92085 17.8683 5.37553 17.8944 6.29839 16.9927C7.17954 16.138 8.04505 15.2624 8.90534 14.3868C9.0409 14.2513 9.13997 14.2096 9.3381 14.2826C10.1775 14.5797 11.0483 14.6579 11.9346 14.5432C11.9659 14.538 11.9972 14.538 12.0702 14.538C12.0024 14.6109 11.9555 14.6631 11.9033 14.71C10.6259 15.9869 9.34331 17.2637 8.07112 18.5458C7.27339 19.3485 6.32446 19.8384 5.19305 19.9687C5.16176 19.9739 5.13569 19.9895 5.10441 19.9999C4.81764 19.9999 4.53088 19.9999 4.24412 19.9999C4.2024 19.9895 4.16069 19.9687 4.11377 19.9635C2.18984 19.6716 0.891577 18.6084 0.229412 16.7843C0.11992 16.4507 0.0782085 16.0911 0 15.7471C0 15.4605 0 15.1738 0 14.8872Z"
                      fill="var(--font-color1)" />
                    <path
                      d="M7.20068 11.1503C7.47702 10.8741 7.71165 10.6239 7.96713 10.3894C8.42595 9.96202 9.09854 9.97244 9.57301 10.4102C9.96405 10.7698 10.4072 11.0044 10.9338 11.0669C11.7003 11.1607 12.3624 10.9418 12.9099 10.3998C13.5512 9.76397 14.1925 9.12292 14.8286 8.48187C15.5429 7.76786 16.2624 7.06427 16.9663 6.33983C18.1968 5.08379 17.6754 2.98344 15.9965 2.47269C15.1206 2.20688 14.3281 2.39451 13.6711 3.03035C12.7743 3.90071 11.8984 4.79193 11.0173 5.67272C10.9495 5.74047 10.9026 5.80823 10.7722 5.76132C9.86499 5.42777 8.92648 5.33917 7.93584 5.46946C7.98277 5.41213 8.00884 5.37044 8.04012 5.33917C9.35924 4.02059 10.6679 2.68637 12.0079 1.38342C13.3322 0.0961116 14.9277 -0.305196 16.6848 0.231618C18.421 0.76322 19.5107 1.97757 19.8861 3.76C20.2146 5.30268 19.834 6.71508 18.7443 7.85646C17.3365 9.33139 15.887 10.7751 14.4115 12.1875C12.5814 13.9386 9.54694 13.8135 7.82635 11.9529C7.59694 11.7132 7.41445 11.4265 7.20068 11.1503Z"
                      fill="var(--font-color1)" />
                  </svg>
                </button>
                <button type="button" (click)="onInsertImage()" title="Добавить изображение"><svg width="21" height="21"
                    viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_160_7117)">
                      <path
                        d="M0 13.9914C0.0687773 13.6566 0.126092 13.3217 0.229258 12.9868C0.458515 12.2016 0.894105 11.5319 1.46725 10.9545C4.1381 8.26396 6.80895 5.57343 9.49127 2.87135C10.4656 1.88982 11.6348 1.22008 13.0104 0.977585C15.4634 0.550333 17.5726 1.23163 19.2691 3.0792C20.6217 4.56881 21.1719 6.3471 20.9656 8.35634C20.8051 9.96142 20.1059 11.324 18.9711 12.4672C16.3117 15.1462 13.6523 17.8136 10.9929 20.5041C10.4198 21.0815 9.64028 20.9083 9.36517 20.3194C9.20469 19.9614 9.23908 19.6265 9.4798 19.3148C9.54858 19.2224 9.62882 19.1531 9.6976 19.0723C12.3226 16.4279 14.9591 13.772 17.5841 11.1277C18.2833 10.4349 18.7762 9.615 18.9711 8.64502C19.315 6.88982 18.8679 5.35403 17.5841 4.10691C16.5409 3.09075 15.2686 2.68659 13.8242 2.81361C12.6779 2.91754 11.6921 3.41407 10.8668 4.23394C8.17303 6.94756 5.47926 9.64964 2.78548 12.3748C1.97162 13.1947 1.71943 14.1877 2.05186 15.2963C2.38428 16.3933 3.14083 17.063 4.25273 17.2824C5.1583 17.4672 5.97216 17.2247 6.68286 16.6242C6.7631 16.555 6.83188 16.4857 6.91212 16.4164C9.34225 13.9683 11.7724 11.5203 14.214 9.07227C14.512 8.77204 14.661 8.44872 14.5349 8.03301C14.3515 7.38636 13.5835 7.13232 13.0448 7.53648C12.9645 7.59421 12.8843 7.67504 12.8155 7.74433C11.2565 9.31477 9.6976 10.8852 8.12718 12.4672C7.70306 12.8944 7.17576 12.9406 6.7631 12.5942C6.37336 12.2709 6.30458 11.682 6.60262 11.2778C6.64847 11.2085 6.70579 11.1508 6.7631 11.0931C8.34498 9.49952 9.92686 7.90599 11.5087 6.31246C12.5175 5.29629 13.9389 5.11153 15.1195 5.83902C16.6326 6.7628 16.9421 8.91061 15.75 10.227C15.6812 10.3078 15.6124 10.3771 15.5437 10.4464C13.125 12.8829 10.6949 15.3309 8.2762 17.7674C7.5655 18.4834 6.72871 18.9683 5.73144 19.1762C3.0262 19.7189 0.401201 17.8252 0.0458515 15.0653C0.0343886 14.9961 0.0229258 14.9268 0 14.869C0 14.5688 0 14.2801 0 13.9914Z"
                        fill="var(--font-color1)" />
                    </g>
                    <defs>
                      <clipPath id="clip0_160_7117">
                        <rect width="21" height="20" fill="white" transform="translate(0 0.850586)" />
                      </clipPath>
                    </defs>
                  </svg>
                </button>
                <button type="button" (click)="resetEditor()" title="Сбросить">⟲</button>
                <div class="smile_">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M11.251 0C11.7502 0 12.2494 0 12.7486 0C12.8126 0.0127833 12.8638 0.0255666 12.9278 0.0383498C13.5422 0.127833 14.1694 0.191749 14.771 0.332365C19.3919 1.39338 23.04 5.25393 23.8208 9.93261C23.8976 10.3672 23.9488 10.8147 24 11.2493C24 11.7478 24 12.2464 24 12.7449C23.9872 12.8088 23.9744 12.8855 23.9616 12.9495C23.8848 13.4864 23.8336 14.0233 23.7184 14.5474C22.1312 21.9745 13.939 26.0907 7.02692 22.9204C1.86843 20.5427 -0.909216 15.0076 0.268403 9.44684C1.27962 4.70425 5.15808 0.997096 9.93256 0.178966C10.3806 0.102266 10.8158 0.0639164 11.251 0ZM12.019 1.87914C6.43811 1.86636 1.89403 6.40442 1.88123 11.9779C1.86843 17.5514 6.3997 22.0895 11.9934 22.1151C17.5743 22.1279 22.144 17.577 22.144 12.0035C22.144 6.41721 17.5999 1.87914 12.019 1.87914Z"
                      fill="var(--book_about)" />
                    <path
                      d="M12.1338 18.05C10.3418 18.0245 9.02335 17.3342 7.92253 16.2093C7.69213 15.9792 7.48732 15.7235 7.29532 15.455C6.94971 14.9821 7.03931 14.4196 7.47452 14.1128C7.92253 13.7932 8.47294 13.8955 8.83134 14.3812C9.43295 15.1994 10.1754 15.8258 11.1738 16.0814C12.3642 16.3882 13.4138 16.0303 14.3226 15.2505C14.6298 14.9821 14.8986 14.6497 15.1546 14.3301C15.4875 13.921 16.0251 13.8188 16.4475 14.1C16.8571 14.3812 17.0107 14.9437 16.7163 15.3656C15.5387 16.9763 14.0154 17.9733 12.1338 18.05Z"
                      fill="var(--book_about)" />
                    <path
                      d="M9.04898 8.90995C9.04898 9.5619 8.51137 10.086 7.87136 10.0732C7.21855 10.0732 6.70654 9.53633 6.70654 8.88438C6.71934 8.24522 7.24415 7.73389 7.87136 7.73389C8.52417 7.73389 9.04898 8.258 9.04898 8.90995Z"
                      fill="var(--book_about)" />
                    <path
                      d="M16.0899 7.73389C16.7427 7.73389 17.2675 8.258 17.2675 8.90995C17.2675 9.5619 16.7299 10.086 16.0899 10.0732C15.4498 10.0732 14.9378 9.54911 14.925 8.90995C14.9122 8.258 15.437 7.73389 16.0899 7.73389Z"
                      fill="var(--book_about)" />
                  </svg>
                </div>
              </div>
              <input type="file" #imageInput accept="image/*" hidden (change)="onImageSelected($event)">
              <div class="editor-content" contenteditable="true" #editorContent
                (input)="updateFormValue(editorContent.innerHTML)" (click)="onSelectionChange()"
                (keyup)="onSelectionChange()" placeholder="Введите ваше сообщение..."></div>
            </div>
            <div class="comment-btn">
              <button class="audio_chip" [disabled]="isCommentSubmiting || commentForm.invalid"
                (click)="submitComment()">Ответить</button>
              <button class="audio_chip" (click)="cancelEdit()">Отменить</button>
            </div>
          </form>
        </div>
        <!-- <div class="ar_wrapp">
          @for(topic of filteredTopics; track topic.id) {
          <div (click)="router.navigate(['/ru/forum/topic/' + topic.id])" class="article-item m">
            <div class="vis_part relative">
              <div class="art_img">
                @if(category.icon) {
                <img style="object-fit: cover" width="66" height="66"
                  [src]="environment.serverUrl + '/upload/' + category.icon.name">
                } @else {
                <img src="assets/images/clouds.webp" alt="image">
                }
              </div>
              <div class="flex justify-between w-full dbl_wr">
                <div class="titl_w">
                  <div class="article-title ov_wrap">
                    {{topic.name}}
                  </div>
                  <div class="flex rticle-category">
                    <div class="article-category">Автор: {{topic.user.firstName}} {{topic.user.lastName}}</div>
                    <div class="article-category ml-6">{{getLastComment(topic.comments)}}</div>
                  </div>
                </div>
                <div class="info_bl">
                  <span>
                    <img src="assets/images/icons/cgfh.svg" alt="check">
                    {{topic.views}} просмотра(ов)
                  </span>
                  <span>
                    <img src="assets/images/icons/fframe.svg" alt="chat">
                    {{topic.comments.length}} ответа(ов)
                  </span>
                </div>
              </div>
            </div>
          </div>
          }
        </div> -->
      </div>
    </div>
  </div>
</div>