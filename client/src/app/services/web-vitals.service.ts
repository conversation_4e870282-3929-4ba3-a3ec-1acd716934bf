import { Injectable, inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

interface WebVitalMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
}

@Injectable({
  providedIn: 'root'
})
export class WebVitalsService {
  private platformId = inject(PLATFORM_ID);

  constructor() {
    if (isPlatformBrowser(this.platformId)) {
      this.initWebVitals();
    }
  }

  private initWebVitals(): void {
    // Monitor Largest Contentful Paint (LCP)
    this.observeLCP();
    
    // Monitor First Input Delay (FID)
    this.observeFID();
    
    // Monitor Cumulative Layout Shift (CLS)
    this.observeCLS();
    
    // Monitor First Contentful Paint (FCP)
    this.observeFCP();
    
    // Monitor Time to First Byte (TTFB)
    this.observeTTFB();
  }

  private observeLCP(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1] as any;
        
        const metric: WebVitalMetric = {
          name: 'LCP',
          value: lastEntry.startTime,
          rating: this.getLCPRating(lastEntry.startTime),
          delta: lastEntry.startTime,
          id: this.generateId()
        };
        
        this.reportMetric(metric);
      });
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    }
  }

  private observeFID(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          const metric: WebVitalMetric = {
            name: 'FID',
            value: entry.processingStart - entry.startTime,
            rating: this.getFIDRating(entry.processingStart - entry.startTime),
            delta: entry.processingStart - entry.startTime,
            id: this.generateId()
          };
          
          this.reportMetric(metric);
        });
      });
      
      observer.observe({ entryTypes: ['first-input'] });
    }
  }

  private observeCLS(): void {
    if ('PerformanceObserver' in window) {
      let clsValue = 0;
      
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        
        const metric: WebVitalMetric = {
          name: 'CLS',
          value: clsValue,
          rating: this.getCLSRating(clsValue),
          delta: clsValue,
          id: this.generateId()
        };
        
        this.reportMetric(metric);
      });
      
      observer.observe({ entryTypes: ['layout-shift'] });
    }
  }

  private observeFCP(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          const metric: WebVitalMetric = {
            name: 'FCP',
            value: entry.startTime,
            rating: this.getFCPRating(entry.startTime),
            delta: entry.startTime,
            id: this.generateId()
          };
          
          this.reportMetric(metric);
        });
      });
      
      observer.observe({ entryTypes: ['paint'] });
    }
  }

  private observeTTFB(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          const metric: WebVitalMetric = {
            name: 'TTFB',
            value: entry.responseStart - entry.requestStart,
            rating: this.getTTFBRating(entry.responseStart - entry.requestStart),
            delta: entry.responseStart - entry.requestStart,
            id: this.generateId()
          };
          
          this.reportMetric(metric);
        });
      });
      
      observer.observe({ entryTypes: ['navigation'] });
    }
  }

  private getLCPRating(value: number): 'good' | 'needs-improvement' | 'poor' {
    if (value <= 2500) return 'good';
    if (value <= 4000) return 'needs-improvement';
    return 'poor';
  }

  private getFIDRating(value: number): 'good' | 'needs-improvement' | 'poor' {
    if (value <= 100) return 'good';
    if (value <= 300) return 'needs-improvement';
    return 'poor';
  }

  private getCLSRating(value: number): 'good' | 'needs-improvement' | 'poor' {
    if (value <= 0.1) return 'good';
    if (value <= 0.25) return 'needs-improvement';
    return 'poor';
  }

  private getFCPRating(value: number): 'good' | 'needs-improvement' | 'poor' {
    if (value <= 1800) return 'good';
    if (value <= 3000) return 'needs-improvement';
    return 'poor';
  }

  private getTTFBRating(value: number): 'good' | 'needs-improvement' | 'poor' {
    if (value <= 800) return 'good';
    if (value <= 1800) return 'needs-improvement';
    return 'poor';
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private reportMetric(metric: WebVitalMetric): void {
    console.log(`${metric.name}: ${metric.value.toFixed(2)}ms (${metric.rating})`);
    
    // Here you could send metrics to analytics service
    // this.analyticsService.track('web-vital', metric);
  }
}
