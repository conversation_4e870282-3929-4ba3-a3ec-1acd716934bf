import { Injectable, inject, DOCUMENT } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class PerformanceService {
  private document = inject(DOCUMENT);

  constructor() {
    this.optimizeResourceLoading();
  }

  private optimizeResourceLoading(): void {
    // Add fetchpriority to critical resources
    this.addFetchPriorityToScripts();
    
    // Preload critical resources
    this.preloadCriticalResources();
    
    // Optimize images
    this.optimizeImages();
  }

  private addFetchPriorityToScripts(): void {
    const scripts = this.document.querySelectorAll('script[src]');
    scripts.forEach((script: HTMLScriptElement) => {
      const src = script.src;
      if (src.includes('main') || src.includes('polyfills')) {
        script.setAttribute('fetchpriority', 'high');
      } else if (src.includes('chunk')) {
        script.setAttribute('fetchpriority', 'low');
      }
    });
  }

  private preloadCriticalResources(): void {
    const criticalResources = [
      { href: '/polyfills-FFHMD2TL.js', as: 'script', priority: 'high' },
      { href: '/main-3UKJ5DTP.js', as: 'script', priority: 'high' },
      { href: '/styles.css', as: 'style', priority: 'high' }
    ];

    criticalResources.forEach(resource => {
      const existingLink = this.document.querySelector(`link[href="${resource.href}"]`);
      if (!existingLink) {
        const link = this.document.createElement('link');
        link.rel = 'preload';
        link.href = resource.href;
        link.as = resource.as;
        if (resource.priority) {
          link.setAttribute('fetchpriority', resource.priority);
        }
        this.document.head.appendChild(link);
      }
    });
  }

  private optimizeImages(): void {
    // Add loading="lazy" to non-critical images
    const images = this.document.querySelectorAll('img');
    images.forEach((img: HTMLImageElement, index) => {
      if (index > 2) { // First 3 images are critical
        img.loading = 'lazy';
      } else {
        img.setAttribute('fetchpriority', 'high');
      }
    });
  }

  // Method to mark LCP element with high priority
  markLCPElement(element: HTMLElement): void {
    if (element.tagName === 'IMG') {
      element.setAttribute('fetchpriority', 'high');
      (element as HTMLImageElement).loading = 'eager';
    }
  }

  // Method to preload route-specific resources
  preloadRouteResources(routeChunks: string[]): void {
    routeChunks.forEach(chunk => {
      const link = this.document.createElement('link');
      link.rel = 'preload';
      link.href = chunk;
      link.as = 'script';
      link.setAttribute('fetchpriority', 'low');
      this.document.head.appendChild(link);
    });
  }
}
