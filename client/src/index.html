<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title></title>
  <meta name="description" content="">
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">

  <!-- Resource hints for performance -->
  <link rel="dns-prefetch" href="//dev.advayta.org">
  <link rel="preconnect" href="//dev.advayta.org" crossorigin>

  <!-- Preload critical resources -->
  <link rel="preload" as="script" href="/polyfills-FFHMD2TL.js" fetchpriority="high">
  <link rel="preload" as="script" href="/main-3UKJ5DTP.js" fetchpriority="high">
  <link rel="preload" as="style" href="/styles.css" fetchpriority="high">

  <!-- Critical CSS inlined above will be here -->
  <style>
    /* Critical above-the-fold styles */
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #ffffff;
    }

    app-root {
      display: block;
      min-height: 100vh;
    }

    /* Critical header styles */
    .header_stunt {
      height: 60px;
    }

    /* Critical layout styles */
    .content-height_wrap {
      min-height: calc(100vh - 120px);
    }
  </style>

  <!-- Critical Path Optimizer - Inline for immediate execution -->
  <script>
    // Critical Path Optimizer - Inline script for immediate execution
    (function() {
      'use strict';

      // Optimize resource loading priorities
      function optimizeResourcePriorities() {
        const scripts = document.querySelectorAll('script[src]');
        scripts.forEach(script => {
          const src = script.src;
          if (src.includes('main') || src.includes('polyfills')) {
            script.setAttribute('fetchpriority', 'high');
          } else if (src.includes('chunk')) {
            script.setAttribute('fetchpriority', 'low');
          }
        });

        const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
        stylesheets.forEach(link => {
          const href = link.href;
          if (href.includes('styles') || href.includes('main')) {
            link.setAttribute('fetchpriority', 'high');
          } else {
            link.setAttribute('fetchpriority', 'low');
          }
        });
      }

      // Remove preloader when app is ready
      function removePreloader() {
        const preloader = document.getElementById('preloader');
        if (preloader) {
          const checkAngular = setInterval(() => {
            const appRoot = document.querySelector('app-root');
            if (appRoot && appRoot.children.length > 0) {
              preloader.classList.add('preloader-hidden');
              setTimeout(() => preloader.remove(), 300);
              clearInterval(checkAngular);
            }
          }, 100);

          setTimeout(() => {
            if (preloader.parentNode) {
              preloader.classList.add('preloader-hidden');
              setTimeout(() => preloader.remove(), 300);
            }
            clearInterval(checkAngular);
          }, 5000);
        }
      }

      // Initialize
      optimizeResourcePriorities();
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', removePreloader);
      } else {
        removePreloader();
      }
      window.addEventListener('load', optimizeResourcePriorities);
    })();
  </script>
</head>
<body>
  <style>
    .preloader {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 1);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      transition: opacity 0.3s ease;
      opacity: 1;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(222, 165, 61, 0.3);
      border-top: 3px solid #dea53d;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      -webkit-animation: spin 1s linear infinite;
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      will-change: transform;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @-webkit-keyframes spin {
      0% { -webkit-transform: rotate(0deg); }
      100% { -webkit-transform: rotate(360deg); }
    }
    
    .preloader-hidden {
      opacity: 0;
      pointer-events: none;
      visibility: hidden;
    }
  </style>
  <div class="preloader" id="preloader">
    <div class="spinner"></div>
  </div>
  <app-root></app-root>
</body>
</html>
