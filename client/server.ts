import { AngularNodeAppEngine, createNodeRequest<PERSON><PERSON><PERSON>, writeResponseToNodeResponse } from '@angular/ssr/node';
import { VERSION } from '@angular/core';
import express from 'express';
import { fileURLToPath } from 'node:url';
import { dirname, resolve } from 'node:path';
import dotenv from 'dotenv'
dotenv.config()

console.log('Angular version:', VERSION.full);

// The Express app is exported so that it can be used by serverless Functions.
export function app(): express.Express {
  const server = express();
  const serverDistFolder = dirname(fileURLToPath(import.meta.url));
  const browserDistFolder = resolve(serverDistFolder, '../browser');

  const angularApp = new AngularNodeAppEngine();

  server.set('view engine', 'html');
  server.set('views', browserDistFolder);

  // Example Express Rest API endpoints
  // server.get('/api/**', (req, res) => { });
  // Serve static files from /browser
  server.get('**', express.static(browserDistFolder, {
    maxAge: '1y',
    index: 'index.html',
  }));

  // All regular routes use the Angular engine
  server.get('**', createNodeRequestHandler(async (req, res) => {
    const response = await angularApp.handle(req);
    if (response) {
      writeResponseToNodeResponse(response, res);
    } else {
      res.statusCode = 404;
      res.end('Page not found');
    }
  }));

  return server;
}

function run(): void {
  const port = process.env['CLIENT_PORT'] ? parseInt(process.env['CLIENT_PORT']) : 4000;
  const host = process.env['HOST'] || 'localhost'
  // Start up the Node server
  const server = app();
  server.listen(port, host, () => {
    console.log(`Node Express server listening on http://${host}:${port}`);
  });
}

run();
