import { VERSION } from '@angular/core';
import dotenv from 'dotenv';
import { app } from './app.js';

dotenv.config();

console.log('Angular version:', VERSION.full);

// The Express app is exported so that it can be used by serverless Functions.
export function getApp() {
  return app;
}

function run(): void {
  const port = process.env['CLIENT_PORT'] ? parseInt(process.env['CLIENT_PORT']) : 4000;
  const host = process.env['HOST'] || 'localhost';

  // Start up the Node server
  app.listen(port, host, () => {
    console.log(`Node Express server listening on http://${host}:${port}`);
  });
}

run();
