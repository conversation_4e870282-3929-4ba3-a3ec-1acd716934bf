class FetchPriorityPlugin {
  constructor(options = {}) {
    this.options = {
      criticalChunks: ['main', 'polyfills', 'runtime'],
      highPriorityChunks: ['chunk-DCENPQ55', 'chunk-SV3EESJM'],
      ...options
    };
  }

  apply(compiler) {
    compiler.hooks.compilation.tap('FetchPriorityPlugin', (compilation) => {
      compilation.hooks.processAssets.tap(
        {
          name: 'FetchPriorityPlugin',
          stage: compilation.PROCESS_ASSETS_STAGE_OPTIMIZE_INLINE,
        },
        (assets) => {
          Object.keys(assets).forEach((filename) => {
            if (filename.endsWith('.html')) {
              const asset = assets[filename];
              let content = asset.source();

              // Add fetchpriority="high" to critical scripts
              this.options.criticalChunks.forEach(chunk => {
                const regex = new RegExp(`<script([^>]*src="[^"]*${chunk}[^"]*"[^>]*)>`, 'gi');
                content = content.replace(regex, '<script$1 fetchpriority="high">');
              });

              // Add fetchpriority="high" to high priority chunks
              this.options.highPriorityChunks.forEach(chunk => {
                const regex = new RegExp(`<script([^>]*src="[^"]*${chunk}[^"]*"[^>]*)>`, 'gi');
                content = content.replace(regex, '<script$1 fetchpriority="high">');
              });

              // Add fetchpriority="low" to other chunks
              content = content.replace(
                /<script([^>]*src="[^"]*chunk-[^"]*"[^>]*(?!fetchpriority)[^>]*)>/gi,
                '<script$1 fetchpriority="low">'
              );

              // Add fetchpriority="high" to critical stylesheets
              content = content.replace(
                /<link([^>]*rel="stylesheet"[^>]*href="[^"]*(?:main|styles)[^"]*"[^>]*)>/gi,
                '<link$1 fetchpriority="high">'
              );

              // Add resource hints for critical resources
              const resourceHints = this.generateResourceHints(compilation);
              content = content.replace(
                '</head>',
                `${resourceHints}\n</head>`
              );

              compilation.updateAsset(filename, {
                source: () => content,
                size: () => content.length,
              });
            }
          });
        }
      );
    });
  }

  generateResourceHints(compilation) {
    const chunks = compilation.chunks;
    const hints = [];

    chunks.forEach(chunk => {
      chunk.files.forEach(file => {
        if (file.endsWith('.js')) {
          const priority = this.getChunkPriority(file);
          if (priority === 'high') {
            hints.push(`  <link rel="preload" as="script" href="/${file}" fetchpriority="high">`);
          } else if (priority === 'medium') {
            hints.push(`  <link rel="prefetch" as="script" href="/${file}">`);
          }
        }
      });
    });

    return hints.join('\n');
  }

  getChunkPriority(filename) {
    if (this.options.criticalChunks.some(chunk => filename.includes(chunk))) {
      return 'high';
    }
    if (this.options.highPriorityChunks.some(chunk => filename.includes(chunk))) {
      return 'high';
    }
    if (filename.includes('chunk-')) {
      return 'low';
    }
    return 'medium';
  }
}

module.exports = FetchPriorityPlugin;
