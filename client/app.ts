import { AngularNodeAppEngine, createNodeRequestH<PERSON><PERSON>, isMainModule, writeResponseToNodeResponse } from '@angular/ssr/node';
import express from 'express';
import { fileURLToPath } from 'node:url';
import { dirname, resolve } from 'node:path';

const serverDistFolder = dirname(fileURLToPath(import.meta.url));
const browserDistFolder = resolve(serverDistFolder, '../browser');

const app = express();
const angularApp = new AngularNodeAppEngine();

app.set('view engine', 'html');
app.set('views', browserDistFolder);

// Serve static files from /browser
app.get('**', express.static(browserDistFolder, {
  maxAge: '1y',
  index: false,
}));

// All regular routes use the Angular engine
app.get('**', createNodeRequestHandler(async (req, res) => {
  const response = await angularApp.handle(req);
  if (response) {
    writeResponseToNodeResponse(response, res);
  } else {
    res.statusCode = 404;
    res.end('Page not found');
  }
}));

export { app, angularApp };

// Start the server if this module is the main module
if (isMainModule(import.meta.url)) {
  const port = process.env['CLIENT_PORT'] ? parseInt(process.env['CLIENT_PORT']) : 4000;
  const host = process.env['HOST'] || 'localhost';
  
  app.listen(port, host, () => {
    console.log(`Node Express server listening on http://${host}:${port}`);
  });
}
