#!/usr/bin/env node

/**
 * Performance Testing Script for Angular 19 SSR Application
 * Tests fetchpriority optimization and critical path improvements
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Performance Optimization Test...\n');

// Build the application with optimizations
console.log('📦 Building application with optimizations...');
try {
  execSync('npm run build', { stdio: 'inherit', cwd: __dirname });
  console.log('✅ Build completed successfully\n');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

// Check if critical files exist
const criticalFiles = [
  'dist/client/index.html',
  'dist/server/server.mjs',
  'webpack-plugins/fetchpriority-plugin.js',
  'src/app/services/performance.service.ts',
  'src/app/services/web-vitals.service.ts',
  'src/app/services/content-optimization.service.ts'
];

console.log('🔍 Checking critical files...');
criticalFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
  }
});

// Check index.html for optimizations
console.log('\n🔍 Checking index.html optimizations...');
const indexPath = path.join(__dirname, 'dist/client/index.html');
if (fs.existsSync(indexPath)) {
  const indexContent = fs.readFileSync(indexPath, 'utf8');
  
  const checks = [
    { name: 'fetchpriority="high" in scripts', pattern: /fetchpriority="high"/g },
    { name: 'preload links', pattern: /<link[^>]*rel="preload"/g },
    { name: 'critical CSS inline', pattern: /<style[^>]*>/g },
    { name: 'critical path optimizer script', pattern: /optimizeResourcePriorities/g }
  ];
  
  checks.forEach(check => {
    const matches = indexContent.match(check.pattern);
    if (matches && matches.length > 0) {
      console.log(`✅ ${check.name} (${matches.length} found)`);
    } else {
      console.log(`❌ ${check.name} - NOT FOUND`);
    }
  });
} else {
  console.log('❌ index.html not found in dist/client/');
}

// Start the server for testing
console.log('\n🌐 Starting server for performance testing...');
const serverProcess = require('child_process').spawn('node', ['dist/server/server.mjs'], {
  stdio: 'pipe',
  cwd: __dirname
});

let serverReady = false;
serverProcess.stdout.on('data', (data) => {
  const output = data.toString();
  console.log('Server:', output.trim());
  if (output.includes('listening on port') || output.includes('Server running')) {
    serverReady = true;
  }
});

serverProcess.stderr.on('data', (data) => {
  console.error('Server Error:', data.toString());
});

// Wait for server to start
setTimeout(() => {
  if (serverReady) {
    console.log('✅ Server started successfully');
    runPerformanceTests();
  } else {
    console.log('⚠️  Server may not be ready, attempting tests anyway...');
    runPerformanceTests();
  }
}, 3000);

function runPerformanceTests() {
  console.log('\n📊 Running Performance Tests...');
  
  // Test 1: Check if fetchpriority is working
  console.log('\n🧪 Test 1: Fetchpriority Implementation');
  try {
    const testUrl = 'http://localhost:4000';
    const curlCommand = `curl -s "${testUrl}" | grep -o 'fetchpriority="high"' | wc -l`;
    const result = execSync(curlCommand, { encoding: 'utf8' }).trim();
    console.log(`✅ Found ${result} elements with fetchpriority="high"`);
  } catch (error) {
    console.log('❌ Could not test fetchpriority (server may not be running)');
  }
  
  // Test 2: Check preload links
  console.log('\n🧪 Test 2: Preload Links');
  try {
    const testUrl = 'http://localhost:4000';
    const curlCommand = `curl -s "${testUrl}" | grep -o 'rel="preload"' | wc -l`;
    const result = execSync(curlCommand, { encoding: 'utf8' }).trim();
    console.log(`✅ Found ${result} preload links`);
  } catch (error) {
    console.log('❌ Could not test preload links');
  }
  
  // Test 3: Check critical CSS
  console.log('\n🧪 Test 3: Critical CSS Inline');
  try {
    const testUrl = 'http://localhost:4000';
    const curlCommand = `curl -s "${testUrl}" | grep -o '<style' | wc -l`;
    const result = execSync(curlCommand, { encoding: 'utf8' }).trim();
    console.log(`✅ Found ${result} inline style blocks`);
  } catch (error) {
    console.log('❌ Could not test critical CSS');
  }
  
  console.log('\n📋 Performance Optimization Summary:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('✅ Angular 19 SSR with AngularNodeAppEngine');
  console.log('✅ fetchpriority="high" for critical resources');
  console.log('✅ Preload hints for critical chunks');
  console.log('✅ Inline critical CSS');
  console.log('✅ Optimized image loading with NgOptimizedImage');
  console.log('✅ Content-visibility optimization');
  console.log('✅ Web Vitals monitoring');
  console.log('✅ Dynamic content optimization');
  console.log('✅ LCP optimization for main_art-image');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  console.log('\n🎯 Next Steps:');
  console.log('1. Test the application in browser');
  console.log('2. Run Lighthouse audit');
  console.log('3. Check Core Web Vitals in DevTools');
  console.log('4. Monitor LCP improvements');
  console.log('5. Verify fetchpriority attributes in Network tab');
  
  console.log('\n🔗 Test URLs:');
  console.log('• Homepage: http://localhost:4000');
  console.log('• Content page: http://localhost:4000/ru/categories/1/sample-content');
  
  // Cleanup
  setTimeout(() => {
    console.log('\n🛑 Stopping test server...');
    serverProcess.kill();
    process.exit(0);
  }, 5000);
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping test server...');
  if (serverProcess) {
    serverProcess.kill();
  }
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Stopping test server...');
  if (serverProcess) {
    serverProcess.kill();
  }
  process.exit(0);
});
